"""
创建SAMURAI ONNX交付包
自动化打包脚本，生成客户交付的完整包
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_delivery_package():
    """创建完整的交付包"""
    
    print("📦 创建SAMURAI ONNX交付包")
    print("=" * 40)
    
    # 交付包目录
    delivery_dir = Path("SAMURAI_ONNX_Delivery")
    
    # 清理旧的交付包
    if delivery_dir.exists():
        shutil.rmtree(delivery_dir)
    
    delivery_dir.mkdir()
    
    # 1. 复制ONNX模型
    print("📁 复制ONNX模型...")
    models_dir = delivery_dir / "onnx_models"
    models_dir.mkdir()
    
    if Path("onnx_models/image_encoder_base_plus.onnx").exists():
        shutil.copy2("onnx_models/image_encoder_base_plus.onnx", models_dir)
        print("   ✅ image_encoder_base_plus.onnx")
    else:
        print("   ❌ ONNX模型文件不存在")
        return False
    
    # 2. 复制推理引擎
    print("🔧 复制推理引擎...")
    engine_dir = delivery_dir / "inference_engine"
    engine_dir.mkdir()
    
    engine_files = [
        "delivery_package/inference_engine/samurai_onnx.py",
        "delivery_package/inference_engine/requirements.txt"
    ]
    
    for file_path in engine_files:
        if Path(file_path).exists():
            shutil.copy2(file_path, engine_dir)
            print(f"   ✅ {Path(file_path).name}")
        else:
            print(f"   ❌ {file_path} 不存在")
    
    # 3. 复制示例代码
    print("📝 复制示例代码...")
    examples_dir = delivery_dir / "examples"
    examples_dir.mkdir()
    
    if Path("delivery_package/examples/simple_demo.py").exists():
        shutil.copy2("delivery_package/examples/simple_demo.py", examples_dir)
        print("   ✅ simple_demo.py")
    
    # 4. 复制文档
    print("📚 复制文档...")
    docs_dir = delivery_dir / "docs"
    docs_dir.mkdir()
    
    doc_files = [
        ("delivery_package/README.md", "README.md"),
        ("ONNX_MIGRATION_GUIDE.md", "TECHNICAL_GUIDE.md"),
        ("ONNX_MIGRATION_REPORT.md", "MIGRATION_REPORT.md")
    ]
    
    for src, dst in doc_files:
        if Path(src).exists():
            shutil.copy2(src, docs_dir / dst)
            print(f"   ✅ {dst}")
    
    # 5. 复制验证工具
    print("🔍 复制验证工具...")
    validation_dir = delivery_dir / "validation"
    validation_dir.mkdir()
    
    if Path("delivery_package/validation/verify_delivery.py").exists():
        shutil.copy2("delivery_package/validation/verify_delivery.py", validation_dir)
        print("   ✅ verify_delivery.py")
    
    # 6. 创建主README
    print("📄 创建主README...")
    create_main_readme(delivery_dir)
    
    # 7. 创建快速开始脚本
    print("🚀 创建快速开始脚本...")
    create_quick_start_script(delivery_dir)
    
    # 8. 创建交付清单
    print("📋 创建交付清单...")
    create_delivery_manifest(delivery_dir)
    
    print(f"\n✅ 交付包创建完成: {delivery_dir}")
    return delivery_dir

def create_main_readme(delivery_dir):
    """创建主README文件"""
    readme_content = f"""# SAMURAI ONNX - 视频目标跟踪系统

## 🎯 快速开始

1. **验证交付包**
   ```bash
   python validation/verify_delivery.py
   ```

2. **安装依赖**
   ```bash
   pip install -r inference_engine/requirements.txt
   ```

3. **运行演示**
   ```bash
   cd examples
   python simple_demo.py
   ```

## 📁 文件结构

- `onnx_models/` - AI模型文件 (264MB)
- `inference_engine/` - 推理引擎代码
- `examples/` - 使用示例
- `docs/` - 详细文档
- `validation/` - 验证工具

## 📖 详细文档

- [README.md](docs/README.md) - 完整使用指南
- [TECHNICAL_GUIDE.md](docs/TECHNICAL_GUIDE.md) - 技术文档
- [MIGRATION_REPORT.md](docs/MIGRATION_REPORT.md) - 移植报告

## 🆘 快速帮助

**问题**: 模型加载失败
**解决**: 检查 `onnx_models/image_encoder_base_plus.onnx` 是否存在

**问题**: 依赖安装失败
**解决**: 使用 `pip install onnxruntime opencv-python numpy`

**问题**: 推理速度慢
**解决**: 考虑使用GPU版本 `pip install onnxruntime-gpu`

## 📞 技术支持

如有问题，请提供：
1. 错误信息截图
2. Python版本和操作系统
3. 验证报告 (运行 `validation/verify_delivery.py`)

---
交付日期: {datetime.now().strftime('%Y-%m-%d')}
版本: v1.0.0
"""
    
    with open(delivery_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def create_quick_start_script(delivery_dir):
    """创建快速开始脚本"""
    script_content = '''#!/usr/bin/env python3
"""
SAMURAI ONNX 快速开始脚本
一键验证和演示
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        return False

def main():
    print("🚀 SAMURAI ONNX 快速开始")
    print("=" * 30)
    
    # 1. 验证交付包
    if not run_command("python validation/verify_delivery.py", "验证交付包"):
        print("请先解决验证问题")
        return
    
    # 2. 安装依赖
    if not run_command("pip install -r inference_engine/requirements.txt", "安装依赖"):
        print("请手动安装依赖")
        return
    
    # 3. 运行演示
    os.chdir("examples")
    if not run_command("python simple_demo.py", "运行演示"):
        print("演示运行失败")
        return
    
    print("\\n🎉 快速开始完成!")
    print("查看生成的视频文件了解跟踪效果")

if __name__ == "__main__":
    main()
'''
    
    with open(delivery_dir / "quick_start.py", "w", encoding="utf-8") as f:
        f.write(script_content)

def create_delivery_manifest(delivery_dir):
    """创建交付清单"""
    manifest = {
        "project": "SAMURAI ONNX",
        "version": "1.0.0",
        "delivery_date": datetime.now().isoformat(),
        "files": [],
        "total_size_mb": 0
    }
    
    # 遍历所有文件
    for root, dirs, files in os.walk(delivery_dir):
        for file in files:
            file_path = Path(root) / file
            relative_path = file_path.relative_to(delivery_dir)
            size_mb = file_path.stat().st_size / (1024 * 1024)
            
            manifest["files"].append({
                "path": str(relative_path),
                "size_mb": round(size_mb, 2)
            })
            manifest["total_size_mb"] += size_mb
    
    manifest["total_size_mb"] = round(manifest["total_size_mb"], 2)
    manifest["file_count"] = len(manifest["files"])
    
    # 保存清单
    import json
    with open(delivery_dir / "DELIVERY_MANIFEST.json", "w", encoding="utf-8") as f:
        json.dump(manifest, f, indent=2, ensure_ascii=False)
    
    # 创建人类可读的清单
    readable_manifest = f"""# SAMURAI ONNX 交付清单

## 基本信息
- 项目: {manifest['project']}
- 版本: {manifest['version']}
- 交付日期: {manifest['delivery_date'][:10]}
- 文件总数: {manifest['file_count']}
- 总大小: {manifest['total_size_mb']} MB

## 文件列表
"""
    
    for file_info in sorted(manifest["files"], key=lambda x: x["path"]):
        readable_manifest += f"- {file_info['path']} ({file_info['size_mb']} MB)\\n"
    
    with open(delivery_dir / "DELIVERY_MANIFEST.md", "w", encoding="utf-8") as f:
        f.write(readable_manifest)

def create_zip_package(delivery_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f"SAMURAI_ONNX_v1.0.0_{datetime.now().strftime('%Y%m%d')}.zip"
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(delivery_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(delivery_dir.parent)
                zipf.write(file_path, arc_name)
    
    zip_size = Path(zip_name).stat().st_size / (1024 * 1024)
    print(f"✅ ZIP包创建完成: {zip_name} ({zip_size:.1f} MB)")
    
    return zip_name

def main():
    """主函数"""
    print("🏭 SAMURAI ONNX 交付包生成器")
    print("=" * 50)
    
    # 创建交付包
    delivery_dir = create_delivery_package()
    if not delivery_dir:
        print("❌ 交付包创建失败")
        return
    
    # 创建ZIP包
    zip_file = create_zip_package(delivery_dir)
    
    print("\\n" + "=" * 50)
    print("🎉 交付包生成完成!")
    print(f"📁 目录: {delivery_dir}")
    print(f"📦 ZIP包: {zip_file}")
    
    print("\\n📋 交付内容:")
    print("✅ ONNX模型文件 (264MB)")
    print("✅ Python推理引擎")
    print("✅ 使用示例和文档")
    print("✅ 验证和测试工具")
    print("✅ 快速开始脚本")
    
    print("\\n📤 客户交付:")
    print(f"发送 {zip_file} 给客户")
    print("客户解压后运行 python quick_start.py 即可开始使用")

if __name__ == "__main__":
    main()
