"""
测试新导出的端到端模型
"""

import onnxruntime as ort
import numpy as np
import time
import os

def test_end_to_end_models():
    """测试端到端模型"""
    
    print("🧪 测试新导出的端到端模型")
    print("=" * 40)
    
    # 检查模型文件
    models = [
        ("轻量级模型", "onnx_models/samurai_lightweight.onnx"),
        ("模拟端到端模型", "onnx_models/samurai_mock_end_to_end.onnx")
    ]
    
    for model_name, model_path in models:
        print(f"\n🔍 测试 {model_name}")
        print("-" * 30)
        
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            continue
        
        try:
            # 加载模型
            session = ort.InferenceSession(model_path)
            print(f"✅ {model_name} 加载成功")
            
            # 获取输入信息
            inputs_info = session.get_inputs()
            print(f"   输入数量: {len(inputs_info)}")
            for i, inp in enumerate(inputs_info):
                print(f"   输入{i+1}: {inp.name} {inp.shape} {inp.type}")
            
            # 获取输出信息
            outputs_info = session.get_outputs()
            print(f"   输出数量: {len(outputs_info)}")
            for i, out in enumerate(outputs_info):
                print(f"   输出{i+1}: {out.name} {out.shape} {out.type}")
            
            # 创建测试输入
            image = np.random.randn(1, 3, 1024, 1024).astype(np.float32)
            point_coords = np.array([[[512.0, 512.0]]], dtype=np.float32)
            point_labels = np.array([[1]], dtype=np.int64)
            box_coords = np.array([[100.0, 100.0, 400.0, 400.0]], dtype=np.float32)
            
            # 根据模型类型准备输入
            if "lightweight" in model_path:
                # 轻量级模型需要所有输入
                inputs = {
                    'image': image,
                    'point_coords': point_coords,
                    'point_labels': point_labels,
                    'box_coords': box_coords
                }
            else:
                # 模拟端到端模型不需要point_coords
                inputs = {
                    'image': image,
                    'point_labels': point_labels,
                    'box_coords': box_coords
                }
            
            # 运行推理
            start_time = time.time()
            outputs = session.run(None, inputs)
            end_time = time.time()
            
            print(f"✅ {model_name} 推理成功")
            print(f"   推理时间: {(end_time - start_time)*1000:.2f}ms")
            
            # 显示输出形状
            for i, output in enumerate(outputs):
                print(f"   输出{i+1}: {output.shape}")
            
            # 检查输出合理性
            if len(outputs) >= 2:
                mask_output = outputs[0]
                iou_output = outputs[1]
                
                # 检查掩码
                if len(mask_output.shape) == 4:
                    print(f"   掩码范围: [{mask_output.min():.3f}, {mask_output.max():.3f}]")
                
                # 检查IoU
                if len(iou_output.shape) >= 1:
                    print(f"   IoU范围: [{iou_output.min():.3f}, {iou_output.max():.3f}]")
            
        except Exception as e:
            print(f"❌ {model_name} 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_with_inference_engine():
    """使用推理引擎测试端到端模型"""
    
    print("\n🚀 使用推理引擎测试端到端模型")
    print("=" * 50)
    
    try:
        from scripts.onnx_inference import SAMURAIONNXPredictor
        
        # 初始化预测器，强制使用端到端模型
        predictor = SAMURAIONNXPredictor("onnx_models", device="cpu", use_end_to_end=True)
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_bbox = (100, 100, 200, 200)
        
        print(f"   测试图像: {test_image.shape}")
        print(f"   测试边界框: {test_bbox}")
        
        # 测试单帧预测
        start_time = time.time()
        mask, confidence, memory_features = predictor.predict_mask(test_image, test_bbox)
        end_time = time.time()
        
        print(f"   ✅ 端到端推理成功")
        print(f"   推理时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"   掩码形状: {mask.shape}")
        print(f"   置信度: {confidence:.3f}")
        print(f"   内存特征: {memory_features.shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 推理引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_performance():
    """比较不同模型的性能"""
    
    print("\n📊 性能对比")
    print("=" * 20)
    
    models_to_test = [
        ("图像编码器", "onnx_models/image_encoder_base_plus.onnx"),
        ("轻量级端到端", "onnx_models/samurai_lightweight.onnx"),
        ("模拟端到端", "onnx_models/samurai_mock_end_to_end.onnx")
    ]
    
    results = {}
    
    for model_name, model_path in models_to_test:
        if not os.path.exists(model_path):
            print(f"⚠️  {model_name}: 模型不存在")
            continue
        
        try:
            session = ort.InferenceSession(model_path)
            
            # 准备输入
            if "image_encoder" in model_path:
                inputs = {'input_image': np.random.randn(1, 3, 1024, 1024).astype(np.float32)}
            elif "lightweight" in model_path:
                inputs = {
                    'image': np.random.randn(1, 3, 1024, 1024).astype(np.float32),
                    'point_coords': np.array([[[512.0, 512.0]]], dtype=np.float32),
                    'point_labels': np.array([[1]], dtype=np.int64),
                    'box_coords': np.array([[100.0, 100.0, 400.0, 400.0]], dtype=np.float32)
                }
            else:  # mock end to end
                inputs = {
                    'image': np.random.randn(1, 3, 1024, 1024).astype(np.float32),
                    'point_labels': np.array([[1]], dtype=np.int64),
                    'box_coords': np.array([[100.0, 100.0, 400.0, 400.0]], dtype=np.float32)
                }
            
            # 预热
            session.run(None, inputs)
            
            # 测试多次取平均
            times = []
            for _ in range(3):
                start_time = time.time()
                outputs = session.run(None, inputs)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)
            
            avg_time = np.mean(times)
            results[model_name] = avg_time
            
            print(f"✅ {model_name}: {avg_time:.2f}ms")
            
        except Exception as e:
            print(f"❌ {model_name}: 测试失败 - {e}")
    
    # 显示性能总结
    if results:
        print(f"\n📈 性能总结:")
        sorted_results = sorted(results.items(), key=lambda x: x[1])
        for model_name, avg_time in sorted_results:
            fps = 1000 / avg_time
            print(f"   {model_name}: {avg_time:.2f}ms ({fps:.2f} FPS)")

def main():
    """主函数"""
    
    print("🎯 SAMURAI 端到端模型完整测试")
    print("=" * 50)
    
    # 测试端到端模型
    test_end_to_end_models()
    
    # 使用推理引擎测试
    inference_success = test_with_inference_engine()
    
    # 性能对比
    compare_performance()
    
    print("\n" + "=" * 50)
    print("🏆 测试总结")
    
    # 检查模型文件
    model_files = [
        "onnx_models/image_encoder_base_plus.onnx",
        "onnx_models/samurai_lightweight.onnx", 
        "onnx_models/samurai_mock_end_to_end.onnx"
    ]
    
    available_models = sum(1 for f in model_files if os.path.exists(f))
    total_models = len(model_files)
    
    print(f"📊 可用模型: {available_models}/{total_models}")
    
    for model_file in model_files:
        status = "✅" if os.path.exists(model_file) else "❌"
        model_name = os.path.basename(model_file)
        print(f"   {status} {model_name}")
    
    if inference_success:
        print("🎉 端到端推理引擎工作正常!")
    else:
        print("⚠️  端到端推理引擎需要调试")
    
    completion_rate = (available_models / total_models) * 100
    print(f"\n📈 系统完成度: {completion_rate:.1f}%")
    
    if completion_rate >= 80:
        print("🚀 系统基本完整，可以进行生产部署!")
    elif completion_rate >= 50:
        print("⚡ 系统部分完整，核心功能可用")
    else:
        print("🔧 系统需要更多组件才能完整工作")

if __name__ == "__main__":
    main()
