"""
独立测试ONNX模型 - 不依赖原始SAMURAI代码
这个脚本证明ONNX模型可以完全独立运行
"""

import numpy as np
import onnxruntime as ort
import time

def test_onnx_model_standalone():
    """测试ONNX模型是否可以独立运行"""
    
    print("🧪 测试ONNX模型独立运行能力")
    print("=" * 50)
    
    # 1. 加载ONNX模型
    model_path = "onnx_models/image_encoder_base_plus.onnx"
    print(f"📁 加载模型: {model_path}")
    
    try:
        session = ort.InferenceSession(model_path)
        print("✅ 模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 2. 查看模型信息
    print("\n📋 模型信息:")
    print(f"   输入数量: {len(session.get_inputs())}")
    print(f"   输出数量: {len(session.get_outputs())}")
    
    for i, input_info in enumerate(session.get_inputs()):
        print(f"   输入{i+1}: {input_info.name}, 形状: {input_info.shape}, 类型: {input_info.type}")
    
    for i, output_info in enumerate(session.get_outputs()):
        print(f"   输出{i+1}: {output_info.name}, 形状: {output_info.shape}, 类型: {output_info.type}")
    
    # 3. 创建测试输入（模拟一张1024x1024的图片）
    print("\n🖼️  创建测试输入 (1024x1024 RGB图像)")
    test_input = np.random.randn(1, 3, 1024, 1024).astype(np.float32)
    print(f"   输入形状: {test_input.shape}")
    print(f"   输入大小: {test_input.nbytes / 1024 / 1024:.1f} MB")
    
    # 4. 运行推理
    print("\n⚡ 运行推理...")
    try:
        start_time = time.time()
        outputs = session.run(None, {'input_image': test_input})
        end_time = time.time()
        
        inference_time = end_time - start_time
        print(f"✅ 推理成功!")
        print(f"   推理时间: {inference_time:.2f} 秒")
        print(f"   输出数量: {len(outputs)}")
        
        for i, output in enumerate(outputs):
            if hasattr(output, 'shape'):
                print(f"   输出{i+1}形状: {output.shape}")
            else:
                print(f"   输出{i+1}类型: {type(output)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        return False

def compare_with_random_baseline():
    """与随机基线比较，证明模型确实在工作"""
    print("\n🎲 与随机基线比较")
    print("=" * 30)
    
    session = ort.InferenceSession("onnx_models/image_encoder_base_plus.onnx")
    
    # 测试两个不同的输入
    input1 = np.random.randn(1, 3, 1024, 1024).astype(np.float32)
    input2 = np.random.randn(1, 3, 1024, 1024).astype(np.float32)
    
    output1 = session.run(None, {'input_image': input1})[0]
    output2 = session.run(None, {'input_image': input2})[0]
    
    # 计算输出差异
    diff = np.mean(np.abs(output1 - output2))
    print(f"不同输入的输出差异: {diff:.6f}")
    
    if diff > 0.001:  # 如果差异足够大，说明模型在真正处理输入
        print("✅ 模型确实在处理不同输入并产生不同输出")
        return True
    else:
        print("⚠️  输出差异很小，可能存在问题")
        return False

def test_performance():
    """测试性能"""
    print("\n⏱️  性能测试")
    print("=" * 20)
    
    session = ort.InferenceSession("onnx_models/image_encoder_base_plus.onnx")
    test_input = np.random.randn(1, 3, 1024, 1024).astype(np.float32)
    
    # 预热
    for _ in range(3):
        session.run(None, {'input_image': test_input})
    
    # 测试多次推理
    times = []
    for i in range(5):
        start = time.time()
        session.run(None, {'input_image': test_input})
        end = time.time()
        times.append(end - start)
        print(f"   第{i+1}次: {times[-1]:.2f}秒")
    
    avg_time = np.mean(times)
    fps = 1.0 / avg_time
    
    print(f"\n📊 性能统计:")
    print(f"   平均推理时间: {avg_time:.2f} 秒")
    print(f"   理论FPS: {fps:.2f}")
    print(f"   是否适合实时处理: {'是' if fps >= 10 else '否 (需要优化)'}")

if __name__ == "__main__":
    print("🚀 SAMURAI ONNX 独立验证测试")
    print("这个测试证明ONNX模型可以完全脱离原始代码运行")
    print("=" * 60)
    
    # 运行所有测试
    success = True
    
    success &= test_onnx_model_standalone()
    success &= compare_with_random_baseline()
    test_performance()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过! ONNX移植成功!")
        print("✅ 模型可以独立运行，不依赖原始PyTorch代码")
        print("✅ 模型正确处理输入并产生有意义的输出")
    else:
        print("❌ 部分测试失败，需要检查问题")
