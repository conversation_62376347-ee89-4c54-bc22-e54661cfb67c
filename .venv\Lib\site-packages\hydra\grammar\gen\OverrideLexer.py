# Generated from /Users/<USER>/dev/hydra/hydra/grammar/OverrideLexer.g4 by ANTLR 4.9.3
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
    from typing import TextIO
else:
    from typing.io import TextIO



def serializedATN():
    with String<PERSON>() as buf:
        buf.write("\3\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964\2\33")
        buf.write("\u0173\b\1\b\1\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6")
        buf.write("\4\7\t\7\4\b\t\b\4\t\t\t\4\n\t\n\4\13\t\13\4\f\t\f\4\r")
        buf.write("\t\r\4\16\t\16\4\17\t\17\4\20\t\20\4\21\t\21\4\22\t\22")
        buf.write("\4\23\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4\27\t\27\4\30")
        buf.write("\t\30\4\31\t\31\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35")
        buf.write("\4\36\t\36\4\37\t\37\4 \t \4!\t!\4\"\t\"\4#\t#\3\2\3\2")
        buf.write("\3\3\3\3\3\4\3\4\3\4\5\4P\n\4\3\4\7\4S\n\4\f\4\16\4V\13")
        buf.write("\4\5\4X\n\4\3\5\3\5\3\5\3\6\3\6\5\6_\n\6\3\6\3\6\3\7\3")
        buf.write("\7\3\b\3\b\3\t\3\t\3\n\3\n\3\13\3\13\3\f\3\f\3\f\3\f\3")
        buf.write("\r\3\r\5\rs\n\r\3\r\3\r\3\r\7\rx\n\r\f\r\16\r{\13\r\3")
        buf.write("\16\3\16\5\16\177\n\16\3\16\3\16\3\16\5\16\u0084\n\16")
        buf.write("\6\16\u0086\n\16\r\16\16\16\u0087\3\17\5\17\u008b\n\17")
        buf.write("\3\17\3\17\5\17\u008f\n\17\3\20\5\20\u0092\n\20\3\20\3")
        buf.write("\20\5\20\u0096\n\20\3\21\5\21\u0099\n\21\3\21\3\21\3\22")
        buf.write("\3\22\5\22\u009f\n\22\3\23\5\23\u00a2\n\23\3\23\3\23\3")
        buf.write("\24\3\24\5\24\u00a8\n\24\3\25\5\25\u00ab\n\25\3\25\3\25")
        buf.write("\3\26\5\26\u00b0\n\26\3\26\3\26\5\26\u00b4\n\26\3\26\3")
        buf.write("\26\3\27\5\27\u00b9\n\27\3\27\3\27\5\27\u00bd\n\27\3\27")
        buf.write("\3\27\3\30\3\30\3\30\3\30\5\30\u00c5\n\30\3\30\3\30\3")
        buf.write("\30\5\30\u00ca\n\30\3\30\7\30\u00cd\n\30\f\30\16\30\u00d0")
        buf.write("\13\30\5\30\u00d2\n\30\3\31\3\31\5\31\u00d6\n\31\3\31")
        buf.write("\3\31\5\31\u00da\n\31\3\31\3\31\5\31\u00de\n\31\3\31\7")
        buf.write("\31\u00e1\n\31\f\31\16\31\u00e4\13\31\3\32\5\32\u00e7")
        buf.write("\n\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\5\32\u00f1")
        buf.write("\n\32\3\33\5\33\u00f4\n\33\3\33\3\33\3\34\3\34\3\34\3")
        buf.write("\34\3\34\3\34\3\34\3\34\3\34\5\34\u0101\n\34\3\35\3\35")
        buf.write("\3\35\3\35\3\35\3\36\3\36\3\37\3\37\5\37\u010c\n\37\3")
        buf.write("\37\3\37\3\37\7\37\u0111\n\37\f\37\16\37\u0114\13\37\3")
        buf.write(" \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3 \3")
        buf.write(" \3 \3 \3 \3 \6 \u012d\n \r \16 \u012e\3!\6!\u0132\n!")
        buf.write("\r!\16!\u0133\3\"\3\"\3\"\7\"\u0139\n\"\f\"\16\"\u013c")
        buf.write("\13\"\3\"\7\"\u013f\n\"\f\"\16\"\u0142\13\"\3\"\3\"\3")
        buf.write("\"\7\"\u0147\n\"\f\"\16\"\u014a\13\"\5\"\u014c\n\"\3\"")
        buf.write("\3\"\3\"\3\"\7\"\u0152\n\"\f\"\16\"\u0155\13\"\3\"\7\"")
        buf.write("\u0158\n\"\f\"\16\"\u015b\13\"\3\"\3\"\3\"\7\"\u0160\n")
        buf.write("\"\f\"\16\"\u0163\13\"\5\"\u0165\n\"\3\"\5\"\u0168\n\"")
        buf.write("\3#\3#\3#\3#\6#\u016e\n#\r#\16#\u016f\3#\3#\4\u0140\u0159")
        buf.write("\2$\4\2\6\2\b\2\n\2\f\3\16\4\20\5\22\6\24\7\26\b\30\2")
        buf.write("\32\t\34\n\36\13 \f\"\r$\16&\17(\20*\21,\2.\2\60\2\62")
        buf.write("\2\64\22\66\238\24:\25<\26>\27@\30B\31D\32F\33\4\2\3\27")
        buf.write("\4\2C\\c|\3\2\62;\3\2\63;\4\2&&aa\5\2&&//aa\4\2GGgg\4")
        buf.write("\2--//\4\2KKkk\4\2PPpp\4\2HHhh\4\2CCcc\4\2VVvv\4\2TTt")
        buf.write("t\4\2WWww\4\2NNnn\4\2UUuu\b\2&\',-/\61AB^^~~\4\2//aa\4")
        buf.write("\2\13\13\"\"\3\2^^\3\2\177\177\2\u01ac\2\f\3\2\2\2\2\16")
        buf.write("\3\2\2\2\2\20\3\2\2\2\2\22\3\2\2\2\2\24\3\2\2\2\2\26\3")
        buf.write("\2\2\2\2\30\3\2\2\2\2\32\3\2\2\2\2\34\3\2\2\2\3\36\3\2")
        buf.write("\2\2\3 \3\2\2\2\3\"\3\2\2\2\3$\3\2\2\2\3&\3\2\2\2\3(\3")
        buf.write("\2\2\2\3*\3\2\2\2\3,\3\2\2\2\3.\3\2\2\2\3\64\3\2\2\2\3")
        buf.write("\66\3\2\2\2\38\3\2\2\2\3:\3\2\2\2\3<\3\2\2\2\3>\3\2\2")
        buf.write("\2\3@\3\2\2\2\3B\3\2\2\2\3D\3\2\2\2\3F\3\2\2\2\4H\3\2")
        buf.write("\2\2\6J\3\2\2\2\bW\3\2\2\2\nY\3\2\2\2\f\\\3\2\2\2\16b")
        buf.write("\3\2\2\2\20d\3\2\2\2\22f\3\2\2\2\24h\3\2\2\2\26j\3\2\2")
        buf.write("\2\30l\3\2\2\2\32r\3\2\2\2\34~\3\2\2\2\36\u008a\3\2\2")
        buf.write("\2 \u0091\3\2\2\2\"\u0098\3\2\2\2$\u009c\3\2\2\2&\u00a1")
        buf.write("\3\2\2\2(\u00a5\3\2\2\2*\u00aa\3\2\2\2,\u00af\3\2\2\2")
        buf.write(".\u00b8\3\2\2\2\60\u00d1\3\2\2\2\62\u00d5\3\2\2\2\64\u00e6")
        buf.write("\3\2\2\2\66\u00f3\3\2\2\28\u0100\3\2\2\2:\u0102\3\2\2")
        buf.write("\2<\u0107\3\2\2\2>\u010b\3\2\2\2@\u012c\3\2\2\2B\u0131")
        buf.write("\3\2\2\2D\u0167\3\2\2\2F\u0169\3\2\2\2HI\t\2\2\2I\5\3")
        buf.write("\2\2\2JK\t\3\2\2K\7\3\2\2\2LX\7\62\2\2MT\t\4\2\2NP\7a")
        buf.write("\2\2ON\3\2\2\2OP\3\2\2\2PQ\3\2\2\2QS\5\6\3\2RO\3\2\2\2")
        buf.write("SV\3\2\2\2TR\3\2\2\2TU\3\2\2\2UX\3\2\2\2VT\3\2\2\2WL\3")
        buf.write("\2\2\2WM\3\2\2\2X\t\3\2\2\2YZ\7^\2\2Z[\7^\2\2[\13\3\2")
        buf.write("\2\2\\^\7?\2\2]_\5B!\2^]\3\2\2\2^_\3\2\2\2_`\3\2\2\2`")
        buf.write("a\b\6\2\2a\r\3\2\2\2bc\7\u0080\2\2c\17\3\2\2\2de\7-\2")
        buf.write("\2e\21\3\2\2\2fg\7B\2\2g\23\3\2\2\2hi\7<\2\2i\25\3\2\2")
        buf.write("\2jk\7\61\2\2k\27\3\2\2\2lm\5>\37\2mn\3\2\2\2no\b\f\3")
        buf.write("\2o\31\3\2\2\2ps\5\4\2\2qs\t\5\2\2rp\3\2\2\2rq\3\2\2\2")
        buf.write("sy\3\2\2\2tx\5\4\2\2ux\5\6\3\2vx\t\6\2\2wt\3\2\2\2wu\3")
        buf.write("\2\2\2wv\3\2\2\2x{\3\2\2\2yw\3\2\2\2yz\3\2\2\2z\33\3\2")
        buf.write("\2\2{y\3\2\2\2|\177\5\32\r\2}\177\5\b\4\2~|\3\2\2\2~}")
        buf.write("\3\2\2\2\177\u0085\3\2\2\2\u0080\u0083\7\60\2\2\u0081")
        buf.write("\u0084\5\32\r\2\u0082\u0084\5\b\4\2\u0083\u0081\3\2\2")
        buf.write("\2\u0083\u0082\3\2\2\2\u0084\u0086\3\2\2\2\u0085\u0080")
        buf.write("\3\2\2\2\u0086\u0087\3\2\2\2\u0087\u0085\3\2\2\2\u0087")
        buf.write("\u0088\3\2\2\2\u0088\35\3\2\2\2\u0089\u008b\5B!\2\u008a")
        buf.write("\u0089\3\2\2\2\u008a\u008b\3\2\2\2\u008b\u008c\3\2\2\2")
        buf.write("\u008c\u008e\7*\2\2\u008d\u008f\5B!\2\u008e\u008d\3\2")
        buf.write("\2\2\u008e\u008f\3\2\2\2\u008f\37\3\2\2\2\u0090\u0092")
        buf.write("\5B!\2\u0091\u0090\3\2\2\2\u0091\u0092\3\2\2\2\u0092\u0093")
        buf.write("\3\2\2\2\u0093\u0095\7.\2\2\u0094\u0096\5B!\2\u0095\u0094")
        buf.write("\3\2\2\2\u0095\u0096\3\2\2\2\u0096!\3\2\2\2\u0097\u0099")
        buf.write("\5B!\2\u0098\u0097\3\2\2\2\u0098\u0099\3\2\2\2\u0099\u009a")
        buf.write("\3\2\2\2\u009a\u009b\7+\2\2\u009b#\3\2\2\2\u009c\u009e")
        buf.write("\7]\2\2\u009d\u009f\5B!\2\u009e\u009d\3\2\2\2\u009e\u009f")
        buf.write("\3\2\2\2\u009f%\3\2\2\2\u00a0\u00a2\5B!\2\u00a1\u00a0")
        buf.write("\3\2\2\2\u00a1\u00a2\3\2\2\2\u00a2\u00a3\3\2\2\2\u00a3")
        buf.write("\u00a4\7_\2\2\u00a4\'\3\2\2\2\u00a5\u00a7\7}\2\2\u00a6")
        buf.write("\u00a8\5B!\2\u00a7\u00a6\3\2\2\2\u00a7\u00a8\3\2\2\2\u00a8")
        buf.write(")\3\2\2\2\u00a9\u00ab\5B!\2\u00aa\u00a9\3\2\2\2\u00aa")
        buf.write("\u00ab\3\2\2\2\u00ab\u00ac\3\2\2\2\u00ac\u00ad\7\177\2")
        buf.write("\2\u00ad+\3\2\2\2\u00ae\u00b0\5B!\2\u00af\u00ae\3\2\2")
        buf.write("\2\u00af\u00b0\3\2\2\2\u00b0\u00b1\3\2\2\2\u00b1\u00b3")
        buf.write("\7<\2\2\u00b2\u00b4\5B!\2\u00b3\u00b2\3\2\2\2\u00b3\u00b4")
        buf.write("\3\2\2\2\u00b4\u00b5\3\2\2\2\u00b5\u00b6\b\26\4\2\u00b6")
        buf.write("-\3\2\2\2\u00b7\u00b9\5B!\2\u00b8\u00b7\3\2\2\2\u00b8")
        buf.write("\u00b9\3\2\2\2\u00b9\u00ba\3\2\2\2\u00ba\u00bc\7?\2\2")
        buf.write("\u00bb\u00bd\5B!\2\u00bc\u00bb\3\2\2\2\u00bc\u00bd\3\2")
        buf.write("\2\2\u00bd\u00be\3\2\2\2\u00be\u00bf\b\27\5\2\u00bf/\3")
        buf.write("\2\2\2\u00c0\u00c1\5\b\4\2\u00c1\u00c2\7\60\2\2\u00c2")
        buf.write("\u00d2\3\2\2\2\u00c3\u00c5\5\b\4\2\u00c4\u00c3\3\2\2\2")
        buf.write("\u00c4\u00c5\3\2\2\2\u00c5\u00c6\3\2\2\2\u00c6\u00c7\7")
        buf.write("\60\2\2\u00c7\u00ce\5\6\3\2\u00c8\u00ca\7a\2\2\u00c9\u00c8")
        buf.write("\3\2\2\2\u00c9\u00ca\3\2\2\2\u00ca\u00cb\3\2\2\2\u00cb")
        buf.write("\u00cd\5\6\3\2\u00cc\u00c9\3\2\2\2\u00cd\u00d0\3\2\2\2")
        buf.write("\u00ce\u00cc\3\2\2\2\u00ce\u00cf\3\2\2\2\u00cf\u00d2\3")
        buf.write("\2\2\2\u00d0\u00ce\3\2\2\2\u00d1\u00c0\3\2\2\2\u00d1\u00c4")
        buf.write("\3\2\2\2\u00d2\61\3\2\2\2\u00d3\u00d6\5\b\4\2\u00d4\u00d6")
        buf.write("\5\60\30\2\u00d5\u00d3\3\2\2\2\u00d5\u00d4\3\2\2\2\u00d6")
        buf.write("\u00d7\3\2\2\2\u00d7\u00d9\t\7\2\2\u00d8\u00da\t\b\2\2")
        buf.write("\u00d9\u00d8\3\2\2\2\u00d9\u00da\3\2\2\2\u00da\u00db\3")
        buf.write("\2\2\2\u00db\u00e2\5\6\3\2\u00dc\u00de\7a\2\2\u00dd\u00dc")
        buf.write("\3\2\2\2\u00dd\u00de\3\2\2\2\u00de\u00df\3\2\2\2\u00df")
        buf.write("\u00e1\5\6\3\2\u00e0\u00dd\3\2\2\2\u00e1\u00e4\3\2\2\2")
        buf.write("\u00e2\u00e0\3\2\2\2\u00e2\u00e3\3\2\2\2\u00e3\63\3\2")
        buf.write("\2\2\u00e4\u00e2\3\2\2\2\u00e5\u00e7\t\b\2\2\u00e6\u00e5")
        buf.write("\3\2\2\2\u00e6\u00e7\3\2\2\2\u00e7\u00f0\3\2\2\2\u00e8")
        buf.write("\u00f1\5\60\30\2\u00e9\u00f1\5\62\31\2\u00ea\u00eb\t\t")
        buf.write("\2\2\u00eb\u00ec\t\n\2\2\u00ec\u00f1\t\13\2\2\u00ed\u00ee")
        buf.write("\t\n\2\2\u00ee\u00ef\t\f\2\2\u00ef\u00f1\t\n\2\2\u00f0")
        buf.write("\u00e8\3\2\2\2\u00f0\u00e9\3\2\2\2\u00f0\u00ea\3\2\2\2")
        buf.write("\u00f0\u00ed\3\2\2\2\u00f1\65\3\2\2\2\u00f2\u00f4\t\b")
        buf.write("\2\2\u00f3\u00f2\3\2\2\2\u00f3\u00f4\3\2\2\2\u00f4\u00f5")
        buf.write("\3\2\2\2\u00f5\u00f6\5\b\4\2\u00f6\67\3\2\2\2\u00f7\u00f8")
        buf.write("\t\r\2\2\u00f8\u00f9\t\16\2\2\u00f9\u00fa\t\17\2\2\u00fa")
        buf.write("\u0101\t\7\2\2\u00fb\u00fc\t\13\2\2\u00fc\u00fd\t\f\2")
        buf.write("\2\u00fd\u00fe\t\20\2\2\u00fe\u00ff\t\21\2\2\u00ff\u0101")
        buf.write("\t\7\2\2\u0100\u00f7\3\2\2\2\u0100\u00fb\3\2\2\2\u0101")
        buf.write("9\3\2\2\2\u0102\u0103\t\n\2\2\u0103\u0104\t\17\2\2\u0104")
        buf.write("\u0105\t\20\2\2\u0105\u0106\t\20\2\2\u0106;\3\2\2\2\u0107")
        buf.write("\u0108\t\22\2\2\u0108=\3\2\2\2\u0109\u010c\5\4\2\2\u010a")
        buf.write("\u010c\7a\2\2\u010b\u0109\3\2\2\2\u010b\u010a\3\2\2\2")
        buf.write("\u010c\u0112\3\2\2\2\u010d\u0111\5\4\2\2\u010e\u0111\5")
        buf.write("\6\3\2\u010f\u0111\t\23\2\2\u0110\u010d\3\2\2\2\u0110")
        buf.write("\u010e\3\2\2\2\u0110\u010f\3\2\2\2\u0111\u0114\3\2\2\2")
        buf.write("\u0112\u0110\3\2\2\2\u0112\u0113\3\2\2\2\u0113?\3\2\2")
        buf.write("\2\u0114\u0112\3\2\2\2\u0115\u012d\5\n\5\2\u0116\u0117")
        buf.write("\7^\2\2\u0117\u012d\7*\2\2\u0118\u0119\7^\2\2\u0119\u012d")
        buf.write("\7+\2\2\u011a\u011b\7^\2\2\u011b\u012d\7]\2\2\u011c\u011d")
        buf.write("\7^\2\2\u011d\u012d\7_\2\2\u011e\u011f\7^\2\2\u011f\u012d")
        buf.write("\7}\2\2\u0120\u0121\7^\2\2\u0121\u012d\7\177\2\2\u0122")
        buf.write("\u0123\7^\2\2\u0123\u012d\7<\2\2\u0124\u0125\7^\2\2\u0125")
        buf.write("\u012d\7?\2\2\u0126\u0127\7^\2\2\u0127\u012d\7.\2\2\u0128")
        buf.write("\u0129\7^\2\2\u0129\u012d\7\"\2\2\u012a\u012b\7^\2\2\u012b")
        buf.write("\u012d\7\13\2\2\u012c\u0115\3\2\2\2\u012c\u0116\3\2\2")
        buf.write("\2\u012c\u0118\3\2\2\2\u012c\u011a\3\2\2\2\u012c\u011c")
        buf.write("\3\2\2\2\u012c\u011e\3\2\2\2\u012c\u0120\3\2\2\2\u012c")
        buf.write("\u0122\3\2\2\2\u012c\u0124\3\2\2\2\u012c\u0126\3\2\2\2")
        buf.write("\u012c\u0128\3\2\2\2\u012c\u012a\3\2\2\2\u012d\u012e\3")
        buf.write("\2\2\2\u012e\u012c\3\2\2\2\u012e\u012f\3\2\2\2\u012fA")
        buf.write("\3\2\2\2\u0130\u0132\t\24\2\2\u0131\u0130\3\2\2\2\u0132")
        buf.write("\u0133\3\2\2\2\u0133\u0131\3\2\2\2\u0133\u0134\3\2\2\2")
        buf.write("\u0134C\3\2\2\2\u0135\u014b\7$\2\2\u0136\u0137\7^\2\2")
        buf.write("\u0137\u0139\7^\2\2\u0138\u0136\3\2\2\2\u0139\u013c\3")
        buf.write("\2\2\2\u013a\u0138\3\2\2\2\u013a\u013b\3\2\2\2\u013b\u014c")
        buf.write("\3\2\2\2\u013c\u013a\3\2\2\2\u013d\u013f\13\2\2\2\u013e")
        buf.write("\u013d\3\2\2\2\u013f\u0142\3\2\2\2\u0140\u0141\3\2\2\2")
        buf.write("\u0140\u013e\3\2\2\2\u0141\u0143\3\2\2\2\u0142\u0140\3")
        buf.write("\2\2\2\u0143\u0148\n\25\2\2\u0144\u0145\7^\2\2\u0145\u0147")
        buf.write("\7^\2\2\u0146\u0144\3\2\2\2\u0147\u014a\3\2\2\2\u0148")
        buf.write("\u0146\3\2\2\2\u0148\u0149\3\2\2\2\u0149\u014c\3\2\2\2")
        buf.write("\u014a\u0148\3\2\2\2\u014b\u013a\3\2\2\2\u014b\u0140\3")
        buf.write("\2\2\2\u014c\u014d\3\2\2\2\u014d\u0168\7$\2\2\u014e\u0164")
        buf.write("\7)\2\2\u014f\u0150\7^\2\2\u0150\u0152\7^\2\2\u0151\u014f")
        buf.write("\3\2\2\2\u0152\u0155\3\2\2\2\u0153\u0151\3\2\2\2\u0153")
        buf.write("\u0154\3\2\2\2\u0154\u0165\3\2\2\2\u0155\u0153\3\2\2\2")
        buf.write("\u0156\u0158\13\2\2\2\u0157\u0156\3\2\2\2\u0158\u015b")
        buf.write("\3\2\2\2\u0159\u015a\3\2\2\2\u0159\u0157\3\2\2\2\u015a")
        buf.write("\u015c\3\2\2\2\u015b\u0159\3\2\2\2\u015c\u0161\n\25\2")
        buf.write("\2\u015d\u015e\7^\2\2\u015e\u0160\7^\2\2\u015f\u015d\3")
        buf.write("\2\2\2\u0160\u0163\3\2\2\2\u0161\u015f\3\2\2\2\u0161\u0162")
        buf.write("\3\2\2\2\u0162\u0165\3\2\2\2\u0163\u0161\3\2\2\2\u0164")
        buf.write("\u0153\3\2\2\2\u0164\u0159\3\2\2\2\u0165\u0166\3\2\2\2")
        buf.write("\u0166\u0168\7)\2\2\u0167\u0135\3\2\2\2\u0167\u014e\3")
        buf.write("\2\2\2\u0168E\3\2\2\2\u0169\u016a\7&\2\2\u016a\u016b\7")
        buf.write("}\2\2\u016b\u016d\3\2\2\2\u016c\u016e\n\26\2\2\u016d\u016c")
        buf.write("\3\2\2\2\u016e\u016f\3\2\2\2\u016f\u016d\3\2\2\2\u016f")
        buf.write("\u0170\3\2\2\2\u0170\u0171\3\2\2\2\u0171\u0172\7\177\2")
        buf.write("\2\u0172G\3\2\2\2\67\2\3OTW^rwy~\u0083\u0087\u008a\u008e")
        buf.write("\u0091\u0095\u0098\u009e\u00a1\u00a7\u00aa\u00af\u00b3")
        buf.write("\u00b8\u00bc\u00c4\u00c9\u00ce\u00d1\u00d5\u00d9\u00dd")
        buf.write("\u00e2\u00e6\u00f0\u00f3\u0100\u010b\u0110\u0112\u012c")
        buf.write("\u012e\u0133\u013a\u0140\u0148\u014b\u0153\u0159\u0161")
        buf.write("\u0164\u0167\u016f\6\4\3\2\t\27\2\t\7\2\t\3\2")
        return buf.getvalue()


class OverrideLexer(Lexer):

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    VALUE_MODE = 1

    EQUAL = 1
    TILDE = 2
    PLUS = 3
    AT = 4
    COLON = 5
    SLASH = 6
    KEY_SPECIAL = 7
    DOT_PATH = 8
    POPEN = 9
    COMMA = 10
    PCLOSE = 11
    BRACKET_OPEN = 12
    BRACKET_CLOSE = 13
    BRACE_OPEN = 14
    BRACE_CLOSE = 15
    FLOAT = 16
    INT = 17
    BOOL = 18
    NULL = 19
    UNQUOTED_CHAR = 20
    ID = 21
    ESC = 22
    WS = 23
    QUOTED_VALUE = 24
    INTERPOLATION = 25

    channelNames = [ u"DEFAULT_TOKEN_CHANNEL", u"HIDDEN" ]

    modeNames = [ "DEFAULT_MODE", "VALUE_MODE" ]

    literalNames = [ "<INVALID>",
            "'~'", "'+'", "'@'", "':'", "'/'" ]

    symbolicNames = [ "<INVALID>",
            "EQUAL", "TILDE", "PLUS", "AT", "COLON", "SLASH", "KEY_SPECIAL", 
            "DOT_PATH", "POPEN", "COMMA", "PCLOSE", "BRACKET_OPEN", "BRACKET_CLOSE", 
            "BRACE_OPEN", "BRACE_CLOSE", "FLOAT", "INT", "BOOL", "NULL", 
            "UNQUOTED_CHAR", "ID", "ESC", "WS", "QUOTED_VALUE", "INTERPOLATION" ]

    ruleNames = [ "CHAR", "DIGIT", "INT_UNSIGNED", "ESC_BACKSLASH", "EQUAL", 
                  "TILDE", "PLUS", "AT", "COLON", "SLASH", "KEY_ID", "KEY_SPECIAL", 
                  "DOT_PATH", "POPEN", "COMMA", "PCLOSE", "BRACKET_OPEN", 
                  "BRACKET_CLOSE", "BRACE_OPEN", "BRACE_CLOSE", "VALUE_COLON", 
                  "VALUE_EQUAL", "POINT_FLOAT", "EXPONENT_FLOAT", "FLOAT", 
                  "INT", "BOOL", "NULL", "UNQUOTED_CHAR", "ID", "ESC", "WS", 
                  "QUOTED_VALUE", "INTERPOLATION" ]

    grammarFileName = "OverrideLexer.g4"

    def __init__(self, input=None, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.9.3")
        self._interp = LexerATNSimulator(self, self.atn, self.decisionsToDFA, PredictionContextCache())
        self._actions = None
        self._predicates = None


