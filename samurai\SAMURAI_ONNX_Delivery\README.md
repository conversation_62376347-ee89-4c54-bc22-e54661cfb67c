# SAMURAI ONNX - 视频目标跟踪系统

## 🎯 快速开始

1. **验证交付包**
   ```bash
   python validation/verify_delivery.py
   ```

2. **安装依赖**
   ```bash
   pip install -r inference_engine/requirements.txt
   ```

3. **运行演示**
   ```bash
   cd examples
   python simple_demo.py
   ```

## 📁 文件结构

- `onnx_models/` - AI模型文件 (264MB)
- `inference_engine/` - 推理引擎代码
- `examples/` - 使用示例
- `docs/` - 详细文档
- `validation/` - 验证工具

## 📖 详细文档

- [README.md](docs/README.md) - 完整使用指南
- [TECHNICAL_GUIDE.md](docs/TECHNICAL_GUIDE.md) - 技术文档
- [MIGRATION_REPORT.md](docs/MIGRATION_REPORT.md) - 移植报告

## 🆘 快速帮助

**问题**: 模型加载失败
**解决**: 检查 `onnx_models/image_encoder_base_plus.onnx` 是否存在

**问题**: 依赖安装失败
**解决**: 使用 `pip install onnxruntime opencv-python numpy`

**问题**: 推理速度慢
**解决**: 考虑使用GPU版本 `pip install onnxruntime-gpu`

## 📞 技术支持

如有问题，请提供：
1. 错误信息截图
2. Python版本和操作系统
3. 验证报告 (运行 `validation/verify_delivery.py`)

---
交付日期: 2025-08-25
版本: v1.0.0
