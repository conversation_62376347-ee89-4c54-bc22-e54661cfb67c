#!/usr/bin/env python3
"""
SAMURAI ONNX Setup Script

Automated setup for SAMURAI ONNX inference.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description="", check=True):
    """Run a shell command with error handling."""
    print(f"Running: {description or cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    print("=== Checking Dependencies ===")
    
    required_packages = [
        ("torch", "PyTorch"),
        ("onnxruntime", "ONNX Runtime"),
        ("opencv-python", "OpenCV"),
        ("numpy", "NumPy"),
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {name} is installed")
        except ImportError:
            print(f"✗ {name} is missing")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(missing_packages, gpu=False):
    """Install missing dependencies."""
    if not missing_packages:
        print("All dependencies are already installed!")
        return True
    
    print(f"=== Installing Dependencies ===")
    
    # Base packages
    base_cmd = f"pip install {' '.join(missing_packages)}"
    
    # GPU-specific packages
    if gpu and "onnxruntime" in missing_packages:
        base_cmd = base_cmd.replace("onnxruntime", "onnxruntime-gpu")
    
    # Additional packages for ONNX optimization
    additional_packages = ["onnx", "onnxoptimizer", "psutil"]
    if gpu:
        additional_packages.append("gputil")
    
    full_cmd = f"{base_cmd} {' '.join(additional_packages)}"
    
    success = run_command(full_cmd, "Installing Python packages")
    
    if success:
        print("✓ Dependencies installed successfully!")
    else:
        print("✗ Failed to install dependencies")
    
    return success

def download_checkpoints():
    """Download SAM2 checkpoints if not present."""
    print("=== Checking SAM2 Checkpoints ===")
    
    checkpoint_dir = Path("sam2/checkpoints")
    if not checkpoint_dir.exists():
        print("Checkpoint directory not found")
        return False
    
    # Check for common checkpoint files
    checkpoint_files = [
        "sam2.1_hiera_b+.pt",
        "sam2.1_hiera_l.pt",
        "sam2.1_hiera_s.pt",
        "sam2.1_hiera_t.pt",
    ]
    
    missing_checkpoints = []
    for checkpoint in checkpoint_files:
        if not (checkpoint_dir / checkpoint).exists():
            missing_checkpoints.append(checkpoint)
        else:
            print(f"✓ {checkpoint} found")
    
    if missing_checkpoints:
        print(f"Missing checkpoints: {missing_checkpoints}")
        print("Running download script...")
        
        # Change to checkpoint directory and run download script
        original_dir = os.getcwd()
        try:
            os.chdir(checkpoint_dir)
            success = run_command("./download_ckpts.sh", "Downloading checkpoints")
            os.chdir(original_dir)
            
            if success:
                print("✓ Checkpoints downloaded successfully!")
            else:
                print("✗ Failed to download checkpoints")
                print("Please run manually: cd sam2/checkpoints && ./download_ckpts.sh")
            
            return success
        except Exception as e:
            os.chdir(original_dir)
            print(f"Error downloading checkpoints: {e}")
            return False
    else:
        print("✓ All checkpoints are present")
        return True

def export_onnx_models(model_size="base_plus", components=None):
    """Export ONNX models."""
    print(f"=== Exporting ONNX Models ({model_size}) ===")
    
    if components is None:
        components = ["image_encoder"]  # Start with image encoder only
    
    components_str = " ".join(components)
    cmd = f"python scripts/export_onnx.py --model_name {model_size} --components {components_str}"
    
    success = run_command(cmd, f"Exporting {components_str}")
    
    if success:
        print("✓ ONNX models exported successfully!")
        
        # List exported models
        onnx_dir = Path("onnx_models")
        if onnx_dir.exists():
            onnx_files = list(onnx_dir.glob("*.onnx"))
            print(f"Exported models ({len(onnx_files)}):")
            for onnx_file in onnx_files:
                size_mb = onnx_file.stat().st_size / (1024 * 1024)
                print(f"  - {onnx_file.name} ({size_mb:.1f} MB)")
    else:
        print("✗ Failed to export ONNX models")
    
    return success

def run_demo():
    """Run the ONNX demo."""
    print("=== Running ONNX Demo ===")
    
    cmd = "python scripts/demo_onnx.py --model_dir onnx_models"
    success = run_command(cmd, "Running ONNX demo")
    
    if success:
        print("✓ Demo completed successfully!")
        print("Check the demo_output/ directory for results")
    else:
        print("✗ Demo failed")
    
    return success

def validate_setup():
    """Validate the ONNX setup."""
    print("=== Validating Setup ===")
    
    # Check if ONNX models exist
    onnx_dir = Path("onnx_models")
    if not onnx_dir.exists():
        print("✗ ONNX models directory not found")
        return False
    
    onnx_files = list(onnx_dir.glob("*.onnx"))
    if not onnx_files:
        print("✗ No ONNX models found")
        return False
    
    print(f"✓ Found {len(onnx_files)} ONNX models")
    
    # Test ONNX Runtime import
    try:
        import onnxruntime as ort
        print(f"✓ ONNX Runtime {ort.__version__} is working")
        
        # Test loading a model
        model_path = onnx_files[0]
        session = ort.InferenceSession(str(model_path))
        print(f"✓ Successfully loaded {model_path.name}")
        
    except Exception as e:
        print(f"✗ ONNX Runtime test failed: {e}")
        return False
    
    print("✓ Setup validation passed!")
    return True

def main():
    parser = argparse.ArgumentParser(description="SAMURAI ONNX Setup")
    parser.add_argument("--gpu", action="store_true", help="Install GPU support")
    parser.add_argument("--model_size", default="base_plus", 
                       choices=["tiny", "small", "base_plus", "large"],
                       help="Model size to export")
    parser.add_argument("--components", nargs="+", default=["image_encoder"],
                       choices=["image_encoder", "prompt_encoder", "mask_decoder", "all"],
                       help="Components to export")
    parser.add_argument("--skip_demo", action="store_true", help="Skip running demo")
    parser.add_argument("--skip_checkpoints", action="store_true", help="Skip checkpoint download")
    
    args = parser.parse_args()
    
    print("SAMURAI ONNX Setup")
    print("==================")
    
    # Step 1: Check dependencies
    missing_packages = check_dependencies()
    
    # Step 2: Install dependencies if needed
    if missing_packages:
        if not install_dependencies(missing_packages, args.gpu):
            print("Setup failed at dependency installation")
            return 1
    
    # Step 3: Download checkpoints
    if not args.skip_checkpoints:
        if not download_checkpoints():
            print("Warning: Checkpoint download failed, but continuing...")
    
    # Step 4: Export ONNX models
    if not export_onnx_models(args.model_size, args.components):
        print("Setup failed at ONNX export")
        return 1
    
    # Step 5: Validate setup
    if not validate_setup():
        print("Setup validation failed")
        return 1
    
    # Step 6: Run demo
    if not args.skip_demo:
        if not run_demo():
            print("Demo failed, but setup is complete")
    
    print("\n" + "="*50)
    print("🎉 SAMURAI ONNX setup completed successfully!")
    print("\nNext steps:")
    print("1. Run demo: python scripts/demo_onnx.py")
    print("2. Use your own video: python scripts/onnx_inference.py --video_path your_video.mp4 --bbox 'x,y,w,h'")
    print("3. Validate accuracy: python scripts/validate_onnx.py --pytorch_config configs/samurai/sam2.1_hiera_b+.yaml --pytorch_checkpoint sam2/checkpoints/sam2.1_hiera_b+.pt --onnx_models onnx_models")
    print("4. Benchmark performance: python scripts/benchmark_onnx.py --model_dir onnx_models")
    print("\nFor more information, see ONNX_MIGRATION_GUIDE.md")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
