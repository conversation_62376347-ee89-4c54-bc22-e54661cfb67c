token literal names:
null
null
'~'
'+'
'@'
':'
'/'
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null

token symbolic names:
null
EQUAL
TILDE
PLUS
AT
COLON
SLASH
KEY_SPECIAL
DOT_PATH
POPEN
COMMA
PCLOSE
BRACKET_OPEN
BRACKET_CLOSE
BRACE_OPEN
BRACE_CLOSE
FLOAT
INT
BOOL
NULL
UNQUOTED_CHAR
ID
ESC
WS
QUOTED_VALUE
INTERPOLATION

rule names:
override
key
packageOrGroup
package
value
element
simpleChoiceSweep
argName
function
listContainer
dictContainer
dictKeyValuePair
primitive
dictKey


atn:
[3, 24715, 42794, 33075, 47597, 16764, 15335, 30598, 22884, 3, 27, 161, 4, 2, 9, 2, 4, 3, 9, 3, 4, 4, 9, 4, 4, 5, 9, 5, 4, 6, 9, 6, 4, 7, 9, 7, 4, 8, 9, 8, 4, 9, 9, 9, 4, 10, 9, 10, 4, 11, 9, 11, 4, 12, 9, 12, 4, 13, 9, 13, 4, 14, 9, 14, 4, 15, 9, 15, 3, 2, 3, 2, 3, 2, 5, 2, 34, 10, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 2, 40, 10, 2, 5, 2, 42, 10, 2, 3, 2, 3, 2, 5, 2, 46, 10, 2, 3, 2, 3, 2, 3, 2, 5, 2, 51, 10, 2, 5, 2, 53, 10, 2, 3, 2, 3, 2, 3, 3, 3, 3, 3, 3, 5, 3, 60, 10, 3, 3, 4, 3, 4, 3, 4, 3, 4, 6, 4, 66, 10, 4, 13, 4, 14, 4, 67, 5, 4, 70, 10, 4, 3, 5, 3, 5, 3, 5, 3, 5, 5, 5, 76, 10, 5, 3, 6, 3, 6, 5, 6, 80, 10, 6, 3, 7, 3, 7, 3, 7, 3, 7, 5, 7, 86, 10, 7, 3, 8, 3, 8, 3, 8, 6, 8, 91, 10, 8, 13, 8, 14, 8, 92, 3, 9, 3, 9, 3, 9, 3, 10, 3, 10, 3, 10, 5, 10, 101, 10, 10, 3, 10, 3, 10, 3, 10, 5, 10, 106, 10, 10, 3, 10, 7, 10, 109, 10, 10, 12, 10, 14, 10, 112, 11, 10, 5, 10, 114, 10, 10, 3, 10, 3, 10, 3, 11, 3, 11, 3, 11, 3, 11, 7, 11, 122, 10, 11, 12, 11, 14, 11, 125, 11, 11, 5, 11, 127, 10, 11, 3, 11, 3, 11, 3, 12, 3, 12, 3, 12, 3, 12, 7, 12, 135, 10, 12, 12, 12, 14, 12, 138, 11, 12, 5, 12, 140, 10, 12, 3, 12, 3, 12, 3, 13, 3, 13, 3, 13, 3, 13, 3, 14, 3, 14, 6, 14, 150, 10, 14, 13, 14, 14, 14, 151, 5, 14, 154, 10, 14, 3, 15, 6, 15, 157, 10, 15, 13, 15, 14, 15, 158, 3, 15, 2, 2, 16, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 2, 4, 5, 2, 7, 7, 18, 25, 27, 27, 3, 2, 18, 25, 2, 175, 2, 52, 3, 2, 2, 2, 4, 56, 3, 2, 2, 2, 6, 69, 3, 2, 2, 2, 8, 75, 3, 2, 2, 2, 10, 79, 3, 2, 2, 2, 12, 85, 3, 2, 2, 2, 14, 87, 3, 2, 2, 2, 16, 94, 3, 2, 2, 2, 18, 97, 3, 2, 2, 2, 20, 117, 3, 2, 2, 2, 22, 130, 3, 2, 2, 2, 24, 143, 3, 2, 2, 2, 26, 153, 3, 2, 2, 2, 28, 156, 3, 2, 2, 2, 30, 31, 5, 4, 3, 2, 31, 33, 7, 3, 2, 2, 32, 34, 5, 10, 6, 2, 33, 32, 3, 2, 2, 2, 33, 34, 3, 2, 2, 2, 34, 53, 3, 2, 2, 2, 35, 36, 7, 4, 2, 2, 36, 41, 5, 4, 3, 2, 37, 39, 7, 3, 2, 2, 38, 40, 5, 10, 6, 2, 39, 38, 3, 2, 2, 2, 39, 40, 3, 2, 2, 2, 40, 42, 3, 2, 2, 2, 41, 37, 3, 2, 2, 2, 41, 42, 3, 2, 2, 2, 42, 53, 3, 2, 2, 2, 43, 45, 7, 5, 2, 2, 44, 46, 7, 5, 2, 2, 45, 44, 3, 2, 2, 2, 45, 46, 3, 2, 2, 2, 46, 47, 3, 2, 2, 2, 47, 48, 5, 4, 3, 2, 48, 50, 7, 3, 2, 2, 49, 51, 5, 10, 6, 2, 50, 49, 3, 2, 2, 2, 50, 51, 3, 2, 2, 2, 51, 53, 3, 2, 2, 2, 52, 30, 3, 2, 2, 2, 52, 35, 3, 2, 2, 2, 52, 43, 3, 2, 2, 2, 53, 54, 3, 2, 2, 2, 54, 55, 7, 2, 2, 3, 55, 3, 3, 2, 2, 2, 56, 59, 5, 6, 4, 2, 57, 58, 7, 6, 2, 2, 58, 60, 5, 8, 5, 2, 59, 57, 3, 2, 2, 2, 59, 60, 3, 2, 2, 2, 60, 5, 3, 2, 2, 2, 61, 70, 5, 8, 5, 2, 62, 65, 7, 23, 2, 2, 63, 64, 7, 8, 2, 2, 64, 66, 7, 23, 2, 2, 65, 63, 3, 2, 2, 2, 66, 67, 3, 2, 2, 2, 67, 65, 3, 2, 2, 2, 67, 68, 3, 2, 2, 2, 68, 70, 3, 2, 2, 2, 69, 61, 3, 2, 2, 2, 69, 62, 3, 2, 2, 2, 70, 7, 3, 2, 2, 2, 71, 76, 3, 2, 2, 2, 72, 76, 7, 23, 2, 2, 73, 76, 7, 9, 2, 2, 74, 76, 7, 10, 2, 2, 75, 71, 3, 2, 2, 2, 75, 72, 3, 2, 2, 2, 75, 73, 3, 2, 2, 2, 75, 74, 3, 2, 2, 2, 76, 9, 3, 2, 2, 2, 77, 80, 5, 12, 7, 2, 78, 80, 5, 14, 8, 2, 79, 77, 3, 2, 2, 2, 79, 78, 3, 2, 2, 2, 80, 11, 3, 2, 2, 2, 81, 86, 5, 26, 14, 2, 82, 86, 5, 20, 11, 2, 83, 86, 5, 22, 12, 2, 84, 86, 5, 18, 10, 2, 85, 81, 3, 2, 2, 2, 85, 82, 3, 2, 2, 2, 85, 83, 3, 2, 2, 2, 85, 84, 3, 2, 2, 2, 86, 13, 3, 2, 2, 2, 87, 90, 5, 12, 7, 2, 88, 89, 7, 12, 2, 2, 89, 91, 5, 12, 7, 2, 90, 88, 3, 2, 2, 2, 91, 92, 3, 2, 2, 2, 92, 90, 3, 2, 2, 2, 92, 93, 3, 2, 2, 2, 93, 15, 3, 2, 2, 2, 94, 95, 7, 23, 2, 2, 95, 96, 7, 3, 2, 2, 96, 17, 3, 2, 2, 2, 97, 98, 7, 23, 2, 2, 98, 113, 7, 11, 2, 2, 99, 101, 5, 16, 9, 2, 100, 99, 3, 2, 2, 2, 100, 101, 3, 2, 2, 2, 101, 102, 3, 2, 2, 2, 102, 110, 5, 12, 7, 2, 103, 105, 7, 12, 2, 2, 104, 106, 5, 16, 9, 2, 105, 104, 3, 2, 2, 2, 105, 106, 3, 2, 2, 2, 106, 107, 3, 2, 2, 2, 107, 109, 5, 12, 7, 2, 108, 103, 3, 2, 2, 2, 109, 112, 3, 2, 2, 2, 110, 108, 3, 2, 2, 2, 110, 111, 3, 2, 2, 2, 111, 114, 3, 2, 2, 2, 112, 110, 3, 2, 2, 2, 113, 100, 3, 2, 2, 2, 113, 114, 3, 2, 2, 2, 114, 115, 3, 2, 2, 2, 115, 116, 7, 13, 2, 2, 116, 19, 3, 2, 2, 2, 117, 126, 7, 14, 2, 2, 118, 123, 5, 12, 7, 2, 119, 120, 7, 12, 2, 2, 120, 122, 5, 12, 7, 2, 121, 119, 3, 2, 2, 2, 122, 125, 3, 2, 2, 2, 123, 121, 3, 2, 2, 2, 123, 124, 3, 2, 2, 2, 124, 127, 3, 2, 2, 2, 125, 123, 3, 2, 2, 2, 126, 118, 3, 2, 2, 2, 126, 127, 3, 2, 2, 2, 127, 128, 3, 2, 2, 2, 128, 129, 7, 15, 2, 2, 129, 21, 3, 2, 2, 2, 130, 139, 7, 16, 2, 2, 131, 136, 5, 24, 13, 2, 132, 133, 7, 12, 2, 2, 133, 135, 5, 24, 13, 2, 134, 132, 3, 2, 2, 2, 135, 138, 3, 2, 2, 2, 136, 134, 3, 2, 2, 2, 136, 137, 3, 2, 2, 2, 137, 140, 3, 2, 2, 2, 138, 136, 3, 2, 2, 2, 139, 131, 3, 2, 2, 2, 139, 140, 3, 2, 2, 2, 140, 141, 3, 2, 2, 2, 141, 142, 7, 17, 2, 2, 142, 23, 3, 2, 2, 2, 143, 144, 5, 28, 15, 2, 144, 145, 7, 7, 2, 2, 145, 146, 5, 12, 7, 2, 146, 25, 3, 2, 2, 2, 147, 154, 7, 26, 2, 2, 148, 150, 9, 2, 2, 2, 149, 148, 3, 2, 2, 2, 150, 151, 3, 2, 2, 2, 151, 149, 3, 2, 2, 2, 151, 152, 3, 2, 2, 2, 152, 154, 3, 2, 2, 2, 153, 147, 3, 2, 2, 2, 153, 149, 3, 2, 2, 2, 154, 27, 3, 2, 2, 2, 155, 157, 9, 3, 2, 2, 156, 155, 3, 2, 2, 2, 157, 158, 3, 2, 2, 2, 158, 156, 3, 2, 2, 2, 158, 159, 3, 2, 2, 2, 159, 29, 3, 2, 2, 2, 26, 33, 39, 41, 45, 50, 52, 59, 67, 69, 75, 79, 85, 92, 100, 105, 110, 113, 123, 126, 136, 139, 151, 153, 158]