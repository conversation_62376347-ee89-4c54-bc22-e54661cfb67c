# Generated from /tmp/build-via-sdist-fm63w174/omegaconf-2.3.0/omegaconf/grammar/OmegaConfGrammarParser.g4 by ANTLR 4.9.3
# encoding: utf-8
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
	from typing import Text<PERSON>
else:
	from typing.io import TextIO


def serializedATN():
    with String<PERSON>() as buf:
        buf.write("\3\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964\3\36")
        buf.write("\u00b7\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7")
        buf.write("\4\b\t\b\4\t\t\t\4\n\t\n\4\13\t\13\4\f\t\f\4\r\t\r\4\16")
        buf.write("\t\16\4\17\t\17\4\20\t\20\4\21\t\21\3\2\3\2\3\2\3\3\3")
        buf.write("\3\3\3\3\4\3\4\3\4\3\4\3\4\3\4\6\4/\n\4\r\4\16\4\60\3")
        buf.write("\5\3\5\3\5\3\5\5\5\67\n\5\3\6\3\6\5\6;\n\6\3\6\3\6\3\7")
        buf.write("\3\7\3\7\3\7\7\7C\n\7\f\7\16\7F\13\7\5\7H\n\7\3\7\3\7")
        buf.write("\3\b\3\b\3\b\3\b\3\t\3\t\3\t\5\tS\n\t\7\tU\n\t\f\t\16")
        buf.write("\tX\13\t\3\t\3\t\5\t\\\n\t\6\t^\n\t\r\t\16\t_\5\tb\n\t")
        buf.write("\3\n\3\n\5\nf\n\n\3\13\3\13\7\13j\n\13\f\13\16\13m\13")
        buf.write("\13\3\13\3\13\3\13\3\13\3\13\5\13t\n\13\3\13\3\13\3\13")
        buf.write("\3\13\3\13\3\13\7\13|\n\13\f\13\16\13\177\13\13\3\13\3")
        buf.write("\13\3\f\3\f\3\f\3\f\5\f\u0087\n\f\3\f\3\f\3\r\3\r\3\r")
        buf.write("\5\r\u008e\n\r\3\16\3\16\5\16\u0092\n\16\3\16\3\16\3\16")
        buf.write("\5\16\u0097\n\16\7\16\u0099\n\16\f\16\16\16\u009c\13\16")
        buf.write("\3\17\3\17\5\17\u00a0\n\17\3\17\3\17\3\20\3\20\3\20\3")
        buf.write("\20\3\20\3\20\3\20\3\20\3\20\3\20\6\20\u00ae\n\20\r\20")
        buf.write("\16\20\u00af\3\21\6\21\u00b3\n\21\r\21\16\21\u00b4\3\21")
        buf.write("\2\2\22\2\4\6\b\n\f\16\20\22\24\26\30\32\34\36 \2\4\3")
        buf.write("\2\t\n\3\2\17\26\2\u00ce\2\"\3\2\2\2\4%\3\2\2\2\6.\3\2")
        buf.write("\2\2\b\66\3\2\2\2\n8\3\2\2\2\f>\3\2\2\2\16K\3\2\2\2\20")
        buf.write("a\3\2\2\2\22e\3\2\2\2\24g\3\2\2\2\26\u0082\3\2\2\2\30")
        buf.write("\u008d\3\2\2\2\32\u0091\3\2\2\2\34\u009d\3\2\2\2\36\u00ad")
        buf.write("\3\2\2\2 \u00b2\3\2\2\2\"#\5\6\4\2#$\7\2\2\3$\3\3\2\2")
        buf.write("\2%&\5\b\5\2&\'\7\2\2\3\'\5\3\2\2\2(/\5\22\n\2)/\7\3\2")
        buf.write("\2*/\7\25\2\2+/\7\4\2\2,/\7\5\2\2-/\7\33\2\2.(\3\2\2\2")
        buf.write(".)\3\2\2\2.*\3\2\2\2.+\3\2\2\2.,\3\2\2\2.-\3\2\2\2/\60")
        buf.write("\3\2\2\2\60.\3\2\2\2\60\61\3\2\2\2\61\7\3\2\2\2\62\67")
        buf.write("\5\36\20\2\63\67\5\34\17\2\64\67\5\n\6\2\65\67\5\f\7\2")
        buf.write("\66\62\3\2\2\2\66\63\3\2\2\2\66\64\3\2\2\2\66\65\3\2\2")
        buf.write("\2\67\t\3\2\2\28:\7\f\2\29;\5\20\t\2:9\3\2\2\2:;\3\2\2")
        buf.write("\2;<\3\2\2\2<=\7\r\2\2=\13\3\2\2\2>G\7\7\2\2?D\5\16\b")
        buf.write("\2@A\7\13\2\2AC\5\16\b\2B@\3\2\2\2CF\3\2\2\2DB\3\2\2\2")
        buf.write("DE\3\2\2\2EH\3\2\2\2FD\3\2\2\2G?\3\2\2\2GH\3\2\2\2HI\3")
        buf.write("\2\2\2IJ\7\b\2\2J\r\3\2\2\2KL\5 \21\2LM\7\16\2\2MN\5\b")
        buf.write("\5\2N\17\3\2\2\2OV\5\b\5\2PR\7\13\2\2QS\5\b\5\2RQ\3\2")
        buf.write("\2\2RS\3\2\2\2SU\3\2\2\2TP\3\2\2\2UX\3\2\2\2VT\3\2\2\2")
        buf.write("VW\3\2\2\2Wb\3\2\2\2XV\3\2\2\2Y[\7\13\2\2Z\\\5\b\5\2[")
        buf.write("Z\3\2\2\2[\\\3\2\2\2\\^\3\2\2\2]Y\3\2\2\2^_\3\2\2\2_]")
        buf.write("\3\2\2\2_`\3\2\2\2`b\3\2\2\2aO\3\2\2\2a]\3\2\2\2b\21\3")
        buf.write("\2\2\2cf\5\24\13\2df\5\26\f\2ec\3\2\2\2ed\3\2\2\2f\23")
        buf.write("\3\2\2\2gk\7\6\2\2hj\7\30\2\2ih\3\2\2\2jm\3\2\2\2ki\3")
        buf.write("\2\2\2kl\3\2\2\2ls\3\2\2\2mk\3\2\2\2nt\5\30\r\2op\7\f")
        buf.write("\2\2pq\5\30\r\2qr\7\r\2\2rt\3\2\2\2sn\3\2\2\2so\3\2\2")
        buf.write("\2t}\3\2\2\2uv\7\30\2\2v|\5\30\r\2wx\7\f\2\2xy\5\30\r")
        buf.write("\2yz\7\r\2\2z|\3\2\2\2{u\3\2\2\2{w\3\2\2\2|\177\3\2\2")
        buf.write("\2}{\3\2\2\2}~\3\2\2\2~\u0080\3\2\2\2\177}\3\2\2\2\u0080")
        buf.write("\u0081\7\27\2\2\u0081\25\3\2\2\2\u0082\u0083\7\6\2\2\u0083")
        buf.write("\u0084\5\32\16\2\u0084\u0086\7\16\2\2\u0085\u0087\5\20")
        buf.write("\t\2\u0086\u0085\3\2\2\2\u0086\u0087\3\2\2\2\u0087\u0088")
        buf.write("\3\2\2\2\u0088\u0089\7\b\2\2\u0089\27\3\2\2\2\u008a\u008e")
        buf.write("\5\22\n\2\u008b\u008e\7\24\2\2\u008c\u008e\7\31\2\2\u008d")
        buf.write("\u008a\3\2\2\2\u008d\u008b\3\2\2\2\u008d\u008c\3\2\2\2")
        buf.write("\u008e\31\3\2\2\2\u008f\u0092\5\22\n\2\u0090\u0092\7\24")
        buf.write("\2\2\u0091\u008f\3\2\2\2\u0091\u0090\3\2\2\2\u0092\u009a")
        buf.write("\3\2\2\2\u0093\u0096\7\30\2\2\u0094\u0097\5\22\n\2\u0095")
        buf.write("\u0097\7\24\2\2\u0096\u0094\3\2\2\2\u0096\u0095\3\2\2")
        buf.write("\2\u0097\u0099\3\2\2\2\u0098\u0093\3\2\2\2\u0099\u009c")
        buf.write("\3\2\2\2\u009a\u0098\3\2\2\2\u009a\u009b\3\2\2\2\u009b")
        buf.write("\33\3\2\2\2\u009c\u009a\3\2\2\2\u009d\u009f\t\2\2\2\u009e")
        buf.write("\u00a0\5\6\4\2\u009f\u009e\3\2\2\2\u009f\u00a0\3\2\2\2")
        buf.write("\u00a0\u00a1\3\2\2\2\u00a1\u00a2\7\32\2\2\u00a2\35\3\2")
        buf.write("\2\2\u00a3\u00ae\7\24\2\2\u00a4\u00ae\7\22\2\2\u00a5\u00ae")
        buf.write("\7\20\2\2\u00a6\u00ae\7\17\2\2\u00a7\u00ae\7\21\2\2\u00a8")
        buf.write("\u00ae\7\23\2\2\u00a9\u00ae\7\16\2\2\u00aa\u00ae\7\25")
        buf.write("\2\2\u00ab\u00ae\7\26\2\2\u00ac\u00ae\5\22\n\2\u00ad\u00a3")
        buf.write("\3\2\2\2\u00ad\u00a4\3\2\2\2\u00ad\u00a5\3\2\2\2\u00ad")
        buf.write("\u00a6\3\2\2\2\u00ad\u00a7\3\2\2\2\u00ad\u00a8\3\2\2\2")
        buf.write("\u00ad\u00a9\3\2\2\2\u00ad\u00aa\3\2\2\2\u00ad\u00ab\3")
        buf.write("\2\2\2\u00ad\u00ac\3\2\2\2\u00ae\u00af\3\2\2\2\u00af\u00ad")
        buf.write("\3\2\2\2\u00af\u00b0\3\2\2\2\u00b0\37\3\2\2\2\u00b1\u00b3")
        buf.write("\t\3\2\2\u00b2\u00b1\3\2\2\2\u00b3\u00b4\3\2\2\2\u00b4")
        buf.write("\u00b2\3\2\2\2\u00b4\u00b5\3\2\2\2\u00b5!\3\2\2\2\33.")
        buf.write("\60\66:DGRV[_aeks{}\u0086\u008d\u0091\u0096\u009a\u009f")
        buf.write("\u00ad\u00af\u00b4")
        return buf.getvalue()


class OmegaConfGrammarParser ( Parser ):

    grammarFileName = "OmegaConfGrammarParser.g4"

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    sharedContextCache = PredictionContextCache()

    literalNames = [ "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                     "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                     "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                     "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                     "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                     "<INVALID>", "<INVALID>", "'.'", "<INVALID>", "<INVALID>", 
                     "<INVALID>", "<INVALID>", "'['", "']'" ]

    symbolicNames = [ "<INVALID>", "ANY_STR", "ESC_INTER", "TOP_ESC", "INTER_OPEN", 
                      "BRACE_OPEN", "BRACE_CLOSE", "QUOTE_OPEN_SINGLE", 
                      "QUOTE_OPEN_DOUBLE", "COMMA", "BRACKET_OPEN", "BRACKET_CLOSE", 
                      "COLON", "FLOAT", "INT", "BOOL", "NULL", "UNQUOTED_CHAR", 
                      "ID", "ESC", "WS", "INTER_CLOSE", "DOT", "INTER_KEY", 
                      "MATCHING_QUOTE_CLOSE", "QUOTED_ESC", "DOLLAR", "INTER_BRACKET_OPEN", 
                      "INTER_BRACKET_CLOSE" ]

    RULE_configValue = 0
    RULE_singleElement = 1
    RULE_text = 2
    RULE_element = 3
    RULE_listContainer = 4
    RULE_dictContainer = 5
    RULE_dictKeyValuePair = 6
    RULE_sequence = 7
    RULE_interpolation = 8
    RULE_interpolationNode = 9
    RULE_interpolationResolver = 10
    RULE_configKey = 11
    RULE_resolverName = 12
    RULE_quotedValue = 13
    RULE_primitive = 14
    RULE_dictKey = 15

    ruleNames =  [ "configValue", "singleElement", "text", "element", "listContainer", 
                   "dictContainer", "dictKeyValuePair", "sequence", "interpolation", 
                   "interpolationNode", "interpolationResolver", "configKey", 
                   "resolverName", "quotedValue", "primitive", "dictKey" ]

    EOF = Token.EOF
    ANY_STR=1
    ESC_INTER=2
    TOP_ESC=3
    INTER_OPEN=4
    BRACE_OPEN=5
    BRACE_CLOSE=6
    QUOTE_OPEN_SINGLE=7
    QUOTE_OPEN_DOUBLE=8
    COMMA=9
    BRACKET_OPEN=10
    BRACKET_CLOSE=11
    COLON=12
    FLOAT=13
    INT=14
    BOOL=15
    NULL=16
    UNQUOTED_CHAR=17
    ID=18
    ESC=19
    WS=20
    INTER_CLOSE=21
    DOT=22
    INTER_KEY=23
    MATCHING_QUOTE_CLOSE=24
    QUOTED_ESC=25
    DOLLAR=26
    INTER_BRACKET_OPEN=27
    INTER_BRACKET_CLOSE=28

    def __init__(self, input:TokenStream, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.9.3")
        self._interp = ParserATNSimulator(self, self.atn, self.decisionsToDFA, self.sharedContextCache)
        self._predicates = None




    class ConfigValueContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def text(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.TextContext,0)


        def EOF(self):
            return self.getToken(OmegaConfGrammarParser.EOF, 0)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_configValue

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterConfigValue" ):
                listener.enterConfigValue(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitConfigValue" ):
                listener.exitConfigValue(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitConfigValue" ):
                return visitor.visitConfigValue(self)
            else:
                return visitor.visitChildren(self)




    def configValue(self):

        localctx = OmegaConfGrammarParser.ConfigValueContext(self, self._ctx, self.state)
        self.enterRule(localctx, 0, self.RULE_configValue)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 32
            self.text()
            self.state = 33
            self.match(OmegaConfGrammarParser.EOF)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class SingleElementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def element(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.ElementContext,0)


        def EOF(self):
            return self.getToken(OmegaConfGrammarParser.EOF, 0)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_singleElement

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterSingleElement" ):
                listener.enterSingleElement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitSingleElement" ):
                listener.exitSingleElement(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitSingleElement" ):
                return visitor.visitSingleElement(self)
            else:
                return visitor.visitChildren(self)




    def singleElement(self):

        localctx = OmegaConfGrammarParser.SingleElementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 2, self.RULE_singleElement)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 35
            self.element()
            self.state = 36
            self.match(OmegaConfGrammarParser.EOF)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class TextContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def interpolation(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(OmegaConfGrammarParser.InterpolationContext)
            else:
                return self.getTypedRuleContext(OmegaConfGrammarParser.InterpolationContext,i)


        def ANY_STR(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ANY_STR)
            else:
                return self.getToken(OmegaConfGrammarParser.ANY_STR, i)

        def ESC(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ESC)
            else:
                return self.getToken(OmegaConfGrammarParser.ESC, i)

        def ESC_INTER(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ESC_INTER)
            else:
                return self.getToken(OmegaConfGrammarParser.ESC_INTER, i)

        def TOP_ESC(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.TOP_ESC)
            else:
                return self.getToken(OmegaConfGrammarParser.TOP_ESC, i)

        def QUOTED_ESC(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.QUOTED_ESC)
            else:
                return self.getToken(OmegaConfGrammarParser.QUOTED_ESC, i)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_text

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterText" ):
                listener.enterText(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitText" ):
                listener.exitText(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitText" ):
                return visitor.visitText(self)
            else:
                return visitor.visitChildren(self)




    def text(self):

        localctx = OmegaConfGrammarParser.TextContext(self, self._ctx, self.state)
        self.enterRule(localctx, 4, self.RULE_text)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 44 
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 44
                self._errHandler.sync(self)
                token = self._input.LA(1)
                if token in [OmegaConfGrammarParser.INTER_OPEN]:
                    self.state = 38
                    self.interpolation()
                    pass
                elif token in [OmegaConfGrammarParser.ANY_STR]:
                    self.state = 39
                    self.match(OmegaConfGrammarParser.ANY_STR)
                    pass
                elif token in [OmegaConfGrammarParser.ESC]:
                    self.state = 40
                    self.match(OmegaConfGrammarParser.ESC)
                    pass
                elif token in [OmegaConfGrammarParser.ESC_INTER]:
                    self.state = 41
                    self.match(OmegaConfGrammarParser.ESC_INTER)
                    pass
                elif token in [OmegaConfGrammarParser.TOP_ESC]:
                    self.state = 42
                    self.match(OmegaConfGrammarParser.TOP_ESC)
                    pass
                elif token in [OmegaConfGrammarParser.QUOTED_ESC]:
                    self.state = 43
                    self.match(OmegaConfGrammarParser.QUOTED_ESC)
                    pass
                else:
                    raise NoViableAltException(self)

                self.state = 46 
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not ((((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.ANY_STR) | (1 << OmegaConfGrammarParser.ESC_INTER) | (1 << OmegaConfGrammarParser.TOP_ESC) | (1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.QUOTED_ESC))) != 0)):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ElementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def primitive(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.PrimitiveContext,0)


        def quotedValue(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.QuotedValueContext,0)


        def listContainer(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.ListContainerContext,0)


        def dictContainer(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.DictContainerContext,0)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_element

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterElement" ):
                listener.enterElement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitElement" ):
                listener.exitElement(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitElement" ):
                return visitor.visitElement(self)
            else:
                return visitor.visitChildren(self)




    def element(self):

        localctx = OmegaConfGrammarParser.ElementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 6, self.RULE_element)
        try:
            self.state = 52
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [OmegaConfGrammarParser.INTER_OPEN, OmegaConfGrammarParser.COLON, OmegaConfGrammarParser.FLOAT, OmegaConfGrammarParser.INT, OmegaConfGrammarParser.BOOL, OmegaConfGrammarParser.NULL, OmegaConfGrammarParser.UNQUOTED_CHAR, OmegaConfGrammarParser.ID, OmegaConfGrammarParser.ESC, OmegaConfGrammarParser.WS]:
                self.enterOuterAlt(localctx, 1)
                self.state = 48
                self.primitive()
                pass
            elif token in [OmegaConfGrammarParser.QUOTE_OPEN_SINGLE, OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE]:
                self.enterOuterAlt(localctx, 2)
                self.state = 49
                self.quotedValue()
                pass
            elif token in [OmegaConfGrammarParser.BRACKET_OPEN]:
                self.enterOuterAlt(localctx, 3)
                self.state = 50
                self.listContainer()
                pass
            elif token in [OmegaConfGrammarParser.BRACE_OPEN]:
                self.enterOuterAlt(localctx, 4)
                self.state = 51
                self.dictContainer()
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ListContainerContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def BRACKET_OPEN(self):
            return self.getToken(OmegaConfGrammarParser.BRACKET_OPEN, 0)

        def BRACKET_CLOSE(self):
            return self.getToken(OmegaConfGrammarParser.BRACKET_CLOSE, 0)

        def sequence(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.SequenceContext,0)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_listContainer

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterListContainer" ):
                listener.enterListContainer(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitListContainer" ):
                listener.exitListContainer(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitListContainer" ):
                return visitor.visitListContainer(self)
            else:
                return visitor.visitChildren(self)




    def listContainer(self):

        localctx = OmegaConfGrammarParser.ListContainerContext(self, self._ctx, self.state)
        self.enterRule(localctx, 8, self.RULE_listContainer)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 54
            self.match(OmegaConfGrammarParser.BRACKET_OPEN)
            self.state = 56
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.BRACE_OPEN) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_SINGLE) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE) | (1 << OmegaConfGrammarParser.COMMA) | (1 << OmegaConfGrammarParser.BRACKET_OPEN) | (1 << OmegaConfGrammarParser.COLON) | (1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0):
                self.state = 55
                self.sequence()


            self.state = 58
            self.match(OmegaConfGrammarParser.BRACKET_CLOSE)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class DictContainerContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def BRACE_OPEN(self):
            return self.getToken(OmegaConfGrammarParser.BRACE_OPEN, 0)

        def BRACE_CLOSE(self):
            return self.getToken(OmegaConfGrammarParser.BRACE_CLOSE, 0)

        def dictKeyValuePair(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(OmegaConfGrammarParser.DictKeyValuePairContext)
            else:
                return self.getTypedRuleContext(OmegaConfGrammarParser.DictKeyValuePairContext,i)


        def COMMA(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.COMMA)
            else:
                return self.getToken(OmegaConfGrammarParser.COMMA, i)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_dictContainer

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterDictContainer" ):
                listener.enterDictContainer(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitDictContainer" ):
                listener.exitDictContainer(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitDictContainer" ):
                return visitor.visitDictContainer(self)
            else:
                return visitor.visitChildren(self)




    def dictContainer(self):

        localctx = OmegaConfGrammarParser.DictContainerContext(self, self._ctx, self.state)
        self.enterRule(localctx, 10, self.RULE_dictContainer)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 60
            self.match(OmegaConfGrammarParser.BRACE_OPEN)
            self.state = 69
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0):
                self.state = 61
                self.dictKeyValuePair()
                self.state = 66
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==OmegaConfGrammarParser.COMMA:
                    self.state = 62
                    self.match(OmegaConfGrammarParser.COMMA)
                    self.state = 63
                    self.dictKeyValuePair()
                    self.state = 68
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)



            self.state = 71
            self.match(OmegaConfGrammarParser.BRACE_CLOSE)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class DictKeyValuePairContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def dictKey(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.DictKeyContext,0)


        def COLON(self):
            return self.getToken(OmegaConfGrammarParser.COLON, 0)

        def element(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.ElementContext,0)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_dictKeyValuePair

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterDictKeyValuePair" ):
                listener.enterDictKeyValuePair(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitDictKeyValuePair" ):
                listener.exitDictKeyValuePair(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitDictKeyValuePair" ):
                return visitor.visitDictKeyValuePair(self)
            else:
                return visitor.visitChildren(self)




    def dictKeyValuePair(self):

        localctx = OmegaConfGrammarParser.DictKeyValuePairContext(self, self._ctx, self.state)
        self.enterRule(localctx, 12, self.RULE_dictKeyValuePair)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 73
            self.dictKey()
            self.state = 74
            self.match(OmegaConfGrammarParser.COLON)
            self.state = 75
            self.element()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class SequenceContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def element(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(OmegaConfGrammarParser.ElementContext)
            else:
                return self.getTypedRuleContext(OmegaConfGrammarParser.ElementContext,i)


        def COMMA(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.COMMA)
            else:
                return self.getToken(OmegaConfGrammarParser.COMMA, i)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_sequence

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterSequence" ):
                listener.enterSequence(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitSequence" ):
                listener.exitSequence(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitSequence" ):
                return visitor.visitSequence(self)
            else:
                return visitor.visitChildren(self)




    def sequence(self):

        localctx = OmegaConfGrammarParser.SequenceContext(self, self._ctx, self.state)
        self.enterRule(localctx, 14, self.RULE_sequence)
        self._la = 0 # Token type
        try:
            self.state = 95
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [OmegaConfGrammarParser.INTER_OPEN, OmegaConfGrammarParser.BRACE_OPEN, OmegaConfGrammarParser.QUOTE_OPEN_SINGLE, OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE, OmegaConfGrammarParser.BRACKET_OPEN, OmegaConfGrammarParser.COLON, OmegaConfGrammarParser.FLOAT, OmegaConfGrammarParser.INT, OmegaConfGrammarParser.BOOL, OmegaConfGrammarParser.NULL, OmegaConfGrammarParser.UNQUOTED_CHAR, OmegaConfGrammarParser.ID, OmegaConfGrammarParser.ESC, OmegaConfGrammarParser.WS]:
                self.enterOuterAlt(localctx, 1)
                self.state = 77
                self.element()
                self.state = 84
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==OmegaConfGrammarParser.COMMA:
                    self.state = 78
                    self.match(OmegaConfGrammarParser.COMMA)
                    self.state = 80
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    if (((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.BRACE_OPEN) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_SINGLE) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE) | (1 << OmegaConfGrammarParser.BRACKET_OPEN) | (1 << OmegaConfGrammarParser.COLON) | (1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0):
                        self.state = 79
                        self.element()


                    self.state = 86
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                pass
            elif token in [OmegaConfGrammarParser.COMMA]:
                self.enterOuterAlt(localctx, 2)
                self.state = 91 
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while True:
                    self.state = 87
                    self.match(OmegaConfGrammarParser.COMMA)
                    self.state = 89
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    if (((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.BRACE_OPEN) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_SINGLE) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE) | (1 << OmegaConfGrammarParser.BRACKET_OPEN) | (1 << OmegaConfGrammarParser.COLON) | (1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0):
                        self.state = 88
                        self.element()


                    self.state = 93 
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    if not (_la==OmegaConfGrammarParser.COMMA):
                        break

                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class InterpolationContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def interpolationNode(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.InterpolationNodeContext,0)


        def interpolationResolver(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.InterpolationResolverContext,0)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_interpolation

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInterpolation" ):
                listener.enterInterpolation(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInterpolation" ):
                listener.exitInterpolation(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitInterpolation" ):
                return visitor.visitInterpolation(self)
            else:
                return visitor.visitChildren(self)




    def interpolation(self):

        localctx = OmegaConfGrammarParser.InterpolationContext(self, self._ctx, self.state)
        self.enterRule(localctx, 16, self.RULE_interpolation)
        try:
            self.state = 99
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,11,self._ctx)
            if la_ == 1:
                self.enterOuterAlt(localctx, 1)
                self.state = 97
                self.interpolationNode()
                pass

            elif la_ == 2:
                self.enterOuterAlt(localctx, 2)
                self.state = 98
                self.interpolationResolver()
                pass


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class InterpolationNodeContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def INTER_OPEN(self):
            return self.getToken(OmegaConfGrammarParser.INTER_OPEN, 0)

        def INTER_CLOSE(self):
            return self.getToken(OmegaConfGrammarParser.INTER_CLOSE, 0)

        def configKey(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(OmegaConfGrammarParser.ConfigKeyContext)
            else:
                return self.getTypedRuleContext(OmegaConfGrammarParser.ConfigKeyContext,i)


        def BRACKET_OPEN(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.BRACKET_OPEN)
            else:
                return self.getToken(OmegaConfGrammarParser.BRACKET_OPEN, i)

        def BRACKET_CLOSE(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.BRACKET_CLOSE)
            else:
                return self.getToken(OmegaConfGrammarParser.BRACKET_CLOSE, i)

        def DOT(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.DOT)
            else:
                return self.getToken(OmegaConfGrammarParser.DOT, i)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_interpolationNode

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInterpolationNode" ):
                listener.enterInterpolationNode(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInterpolationNode" ):
                listener.exitInterpolationNode(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitInterpolationNode" ):
                return visitor.visitInterpolationNode(self)
            else:
                return visitor.visitChildren(self)




    def interpolationNode(self):

        localctx = OmegaConfGrammarParser.InterpolationNodeContext(self, self._ctx, self.state)
        self.enterRule(localctx, 18, self.RULE_interpolationNode)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 101
            self.match(OmegaConfGrammarParser.INTER_OPEN)
            self.state = 105
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==OmegaConfGrammarParser.DOT:
                self.state = 102
                self.match(OmegaConfGrammarParser.DOT)
                self.state = 107
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 113
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [OmegaConfGrammarParser.INTER_OPEN, OmegaConfGrammarParser.ID, OmegaConfGrammarParser.INTER_KEY]:
                self.state = 108
                self.configKey()
                pass
            elif token in [OmegaConfGrammarParser.BRACKET_OPEN]:
                self.state = 109
                self.match(OmegaConfGrammarParser.BRACKET_OPEN)
                self.state = 110
                self.configKey()
                self.state = 111
                self.match(OmegaConfGrammarParser.BRACKET_CLOSE)
                pass
            else:
                raise NoViableAltException(self)

            self.state = 123
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==OmegaConfGrammarParser.BRACKET_OPEN or _la==OmegaConfGrammarParser.DOT:
                self.state = 121
                self._errHandler.sync(self)
                token = self._input.LA(1)
                if token in [OmegaConfGrammarParser.DOT]:
                    self.state = 115
                    self.match(OmegaConfGrammarParser.DOT)
                    self.state = 116
                    self.configKey()
                    pass
                elif token in [OmegaConfGrammarParser.BRACKET_OPEN]:
                    self.state = 117
                    self.match(OmegaConfGrammarParser.BRACKET_OPEN)
                    self.state = 118
                    self.configKey()
                    self.state = 119
                    self.match(OmegaConfGrammarParser.BRACKET_CLOSE)
                    pass
                else:
                    raise NoViableAltException(self)

                self.state = 125
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 126
            self.match(OmegaConfGrammarParser.INTER_CLOSE)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class InterpolationResolverContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def INTER_OPEN(self):
            return self.getToken(OmegaConfGrammarParser.INTER_OPEN, 0)

        def resolverName(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.ResolverNameContext,0)


        def COLON(self):
            return self.getToken(OmegaConfGrammarParser.COLON, 0)

        def BRACE_CLOSE(self):
            return self.getToken(OmegaConfGrammarParser.BRACE_CLOSE, 0)

        def sequence(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.SequenceContext,0)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_interpolationResolver

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInterpolationResolver" ):
                listener.enterInterpolationResolver(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInterpolationResolver" ):
                listener.exitInterpolationResolver(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitInterpolationResolver" ):
                return visitor.visitInterpolationResolver(self)
            else:
                return visitor.visitChildren(self)




    def interpolationResolver(self):

        localctx = OmegaConfGrammarParser.InterpolationResolverContext(self, self._ctx, self.state)
        self.enterRule(localctx, 20, self.RULE_interpolationResolver)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 128
            self.match(OmegaConfGrammarParser.INTER_OPEN)
            self.state = 129
            self.resolverName()
            self.state = 130
            self.match(OmegaConfGrammarParser.COLON)
            self.state = 132
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.BRACE_OPEN) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_SINGLE) | (1 << OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE) | (1 << OmegaConfGrammarParser.COMMA) | (1 << OmegaConfGrammarParser.BRACKET_OPEN) | (1 << OmegaConfGrammarParser.COLON) | (1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0):
                self.state = 131
                self.sequence()


            self.state = 134
            self.match(OmegaConfGrammarParser.BRACE_CLOSE)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ConfigKeyContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def interpolation(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.InterpolationContext,0)


        def ID(self):
            return self.getToken(OmegaConfGrammarParser.ID, 0)

        def INTER_KEY(self):
            return self.getToken(OmegaConfGrammarParser.INTER_KEY, 0)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_configKey

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterConfigKey" ):
                listener.enterConfigKey(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitConfigKey" ):
                listener.exitConfigKey(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitConfigKey" ):
                return visitor.visitConfigKey(self)
            else:
                return visitor.visitChildren(self)




    def configKey(self):

        localctx = OmegaConfGrammarParser.ConfigKeyContext(self, self._ctx, self.state)
        self.enterRule(localctx, 22, self.RULE_configKey)
        try:
            self.state = 139
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [OmegaConfGrammarParser.INTER_OPEN]:
                self.enterOuterAlt(localctx, 1)
                self.state = 136
                self.interpolation()
                pass
            elif token in [OmegaConfGrammarParser.ID]:
                self.enterOuterAlt(localctx, 2)
                self.state = 137
                self.match(OmegaConfGrammarParser.ID)
                pass
            elif token in [OmegaConfGrammarParser.INTER_KEY]:
                self.enterOuterAlt(localctx, 3)
                self.state = 138
                self.match(OmegaConfGrammarParser.INTER_KEY)
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ResolverNameContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def interpolation(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(OmegaConfGrammarParser.InterpolationContext)
            else:
                return self.getTypedRuleContext(OmegaConfGrammarParser.InterpolationContext,i)


        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ID)
            else:
                return self.getToken(OmegaConfGrammarParser.ID, i)

        def DOT(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.DOT)
            else:
                return self.getToken(OmegaConfGrammarParser.DOT, i)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_resolverName

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterResolverName" ):
                listener.enterResolverName(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitResolverName" ):
                listener.exitResolverName(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitResolverName" ):
                return visitor.visitResolverName(self)
            else:
                return visitor.visitChildren(self)




    def resolverName(self):

        localctx = OmegaConfGrammarParser.ResolverNameContext(self, self._ctx, self.state)
        self.enterRule(localctx, 24, self.RULE_resolverName)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 143
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [OmegaConfGrammarParser.INTER_OPEN]:
                self.state = 141
                self.interpolation()
                pass
            elif token in [OmegaConfGrammarParser.ID]:
                self.state = 142
                self.match(OmegaConfGrammarParser.ID)
                pass
            else:
                raise NoViableAltException(self)

            self.state = 152
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==OmegaConfGrammarParser.DOT:
                self.state = 145
                self.match(OmegaConfGrammarParser.DOT)
                self.state = 148
                self._errHandler.sync(self)
                token = self._input.LA(1)
                if token in [OmegaConfGrammarParser.INTER_OPEN]:
                    self.state = 146
                    self.interpolation()
                    pass
                elif token in [OmegaConfGrammarParser.ID]:
                    self.state = 147
                    self.match(OmegaConfGrammarParser.ID)
                    pass
                else:
                    raise NoViableAltException(self)

                self.state = 154
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class QuotedValueContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def MATCHING_QUOTE_CLOSE(self):
            return self.getToken(OmegaConfGrammarParser.MATCHING_QUOTE_CLOSE, 0)

        def QUOTE_OPEN_SINGLE(self):
            return self.getToken(OmegaConfGrammarParser.QUOTE_OPEN_SINGLE, 0)

        def QUOTE_OPEN_DOUBLE(self):
            return self.getToken(OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE, 0)

        def text(self):
            return self.getTypedRuleContext(OmegaConfGrammarParser.TextContext,0)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_quotedValue

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterQuotedValue" ):
                listener.enterQuotedValue(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitQuotedValue" ):
                listener.exitQuotedValue(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitQuotedValue" ):
                return visitor.visitQuotedValue(self)
            else:
                return visitor.visitChildren(self)




    def quotedValue(self):

        localctx = OmegaConfGrammarParser.QuotedValueContext(self, self._ctx, self.state)
        self.enterRule(localctx, 26, self.RULE_quotedValue)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 155
            _la = self._input.LA(1)
            if not(_la==OmegaConfGrammarParser.QUOTE_OPEN_SINGLE or _la==OmegaConfGrammarParser.QUOTE_OPEN_DOUBLE):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
            self.state = 157
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.ANY_STR) | (1 << OmegaConfGrammarParser.ESC_INTER) | (1 << OmegaConfGrammarParser.TOP_ESC) | (1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.QUOTED_ESC))) != 0):
                self.state = 156
                self.text()


            self.state = 159
            self.match(OmegaConfGrammarParser.MATCHING_QUOTE_CLOSE)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class PrimitiveContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ID)
            else:
                return self.getToken(OmegaConfGrammarParser.ID, i)

        def NULL(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.NULL)
            else:
                return self.getToken(OmegaConfGrammarParser.NULL, i)

        def INT(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.INT)
            else:
                return self.getToken(OmegaConfGrammarParser.INT, i)

        def FLOAT(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.FLOAT)
            else:
                return self.getToken(OmegaConfGrammarParser.FLOAT, i)

        def BOOL(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.BOOL)
            else:
                return self.getToken(OmegaConfGrammarParser.BOOL, i)

        def UNQUOTED_CHAR(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.UNQUOTED_CHAR)
            else:
                return self.getToken(OmegaConfGrammarParser.UNQUOTED_CHAR, i)

        def COLON(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.COLON)
            else:
                return self.getToken(OmegaConfGrammarParser.COLON, i)

        def ESC(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ESC)
            else:
                return self.getToken(OmegaConfGrammarParser.ESC, i)

        def WS(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.WS)
            else:
                return self.getToken(OmegaConfGrammarParser.WS, i)

        def interpolation(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(OmegaConfGrammarParser.InterpolationContext)
            else:
                return self.getTypedRuleContext(OmegaConfGrammarParser.InterpolationContext,i)


        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_primitive

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterPrimitive" ):
                listener.enterPrimitive(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitPrimitive" ):
                listener.exitPrimitive(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitPrimitive" ):
                return visitor.visitPrimitive(self)
            else:
                return visitor.visitChildren(self)




    def primitive(self):

        localctx = OmegaConfGrammarParser.PrimitiveContext(self, self._ctx, self.state)
        self.enterRule(localctx, 28, self.RULE_primitive)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 171 
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 171
                self._errHandler.sync(self)
                token = self._input.LA(1)
                if token in [OmegaConfGrammarParser.ID]:
                    self.state = 161
                    self.match(OmegaConfGrammarParser.ID)
                    pass
                elif token in [OmegaConfGrammarParser.NULL]:
                    self.state = 162
                    self.match(OmegaConfGrammarParser.NULL)
                    pass
                elif token in [OmegaConfGrammarParser.INT]:
                    self.state = 163
                    self.match(OmegaConfGrammarParser.INT)
                    pass
                elif token in [OmegaConfGrammarParser.FLOAT]:
                    self.state = 164
                    self.match(OmegaConfGrammarParser.FLOAT)
                    pass
                elif token in [OmegaConfGrammarParser.BOOL]:
                    self.state = 165
                    self.match(OmegaConfGrammarParser.BOOL)
                    pass
                elif token in [OmegaConfGrammarParser.UNQUOTED_CHAR]:
                    self.state = 166
                    self.match(OmegaConfGrammarParser.UNQUOTED_CHAR)
                    pass
                elif token in [OmegaConfGrammarParser.COLON]:
                    self.state = 167
                    self.match(OmegaConfGrammarParser.COLON)
                    pass
                elif token in [OmegaConfGrammarParser.ESC]:
                    self.state = 168
                    self.match(OmegaConfGrammarParser.ESC)
                    pass
                elif token in [OmegaConfGrammarParser.WS]:
                    self.state = 169
                    self.match(OmegaConfGrammarParser.WS)
                    pass
                elif token in [OmegaConfGrammarParser.INTER_OPEN]:
                    self.state = 170
                    self.interpolation()
                    pass
                else:
                    raise NoViableAltException(self)

                self.state = 173 
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not ((((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.INTER_OPEN) | (1 << OmegaConfGrammarParser.COLON) | (1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0)):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class DictKeyContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ID)
            else:
                return self.getToken(OmegaConfGrammarParser.ID, i)

        def NULL(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.NULL)
            else:
                return self.getToken(OmegaConfGrammarParser.NULL, i)

        def INT(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.INT)
            else:
                return self.getToken(OmegaConfGrammarParser.INT, i)

        def FLOAT(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.FLOAT)
            else:
                return self.getToken(OmegaConfGrammarParser.FLOAT, i)

        def BOOL(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.BOOL)
            else:
                return self.getToken(OmegaConfGrammarParser.BOOL, i)

        def UNQUOTED_CHAR(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.UNQUOTED_CHAR)
            else:
                return self.getToken(OmegaConfGrammarParser.UNQUOTED_CHAR, i)

        def ESC(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.ESC)
            else:
                return self.getToken(OmegaConfGrammarParser.ESC, i)

        def WS(self, i:int=None):
            if i is None:
                return self.getTokens(OmegaConfGrammarParser.WS)
            else:
                return self.getToken(OmegaConfGrammarParser.WS, i)

        def getRuleIndex(self):
            return OmegaConfGrammarParser.RULE_dictKey

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterDictKey" ):
                listener.enterDictKey(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitDictKey" ):
                listener.exitDictKey(self)

        def accept(self, visitor:ParseTreeVisitor):
            if hasattr( visitor, "visitDictKey" ):
                return visitor.visitDictKey(self)
            else:
                return visitor.visitChildren(self)




    def dictKey(self):

        localctx = OmegaConfGrammarParser.DictKeyContext(self, self._ctx, self.state)
        self.enterRule(localctx, 30, self.RULE_dictKey)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 176 
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 175
                _la = self._input.LA(1)
                if not((((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0)):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()
                self.state = 178 
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not ((((_la) & ~0x3f) == 0 and ((1 << _la) & ((1 << OmegaConfGrammarParser.FLOAT) | (1 << OmegaConfGrammarParser.INT) | (1 << OmegaConfGrammarParser.BOOL) | (1 << OmegaConfGrammarParser.NULL) | (1 << OmegaConfGrammarParser.UNQUOTED_CHAR) | (1 << OmegaConfGrammarParser.ID) | (1 << OmegaConfGrammarParser.ESC) | (1 << OmegaConfGrammarParser.WS))) != 0)):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx





