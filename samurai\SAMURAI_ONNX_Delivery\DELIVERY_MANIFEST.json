{"project": "SAMURAI ONNX", "version": "1.0.0", "delivery_date": "2025-08-25T17:47:37.013542", "files": [{"path": "quick_start.py", "size_mb": 0.0}, {"path": "README.md", "size_mb": 0.0}, {"path": "docs\\MIGRATION_REPORT.md", "size_mb": 0.01}, {"path": "docs\\README.md", "size_mb": 0.0}, {"path": "docs\\TECHNICAL_GUIDE.md", "size_mb": 0.01}, {"path": "examples\\simple_demo.py", "size_mb": 0.0}, {"path": "inference_engine\\requirements.txt", "size_mb": 0.0}, {"path": "inference_engine\\samurai_onnx.py", "size_mb": 0.01}, {"path": "onnx_models\\image_encoder_base_plus.onnx", "size_mb": 264.31}, {"path": "validation\\verify_delivery.py", "size_mb": 0.01}], "total_size_mb": 264.35, "file_count": 10}