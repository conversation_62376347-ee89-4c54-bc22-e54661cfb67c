"""
SAMURAI ONNX 简单演示
最简单的使用示例，客户可以直接运行
"""

import sys
import os
sys.path.append('../inference_engine')

from samurai_onnx import SAMURAITracker, quick_track
import cv2
import numpy as np

def create_test_video(output_path="test_video.mp4", duration=3, fps=30):
    """创建一个简单的测试视频"""
    print("🎬 创建测试视频...")
    
    width, height = 640, 480
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    total_frames = duration * fps
    
    for frame_idx in range(total_frames):
        # 创建背景
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        frame[:] = (50, 50, 50)  # 深灰色背景
        
        # 移动的绿色圆圈
        t = frame_idx / total_frames
        center_x = int(100 + (width - 200) * t)
        center_y = int(height // 2)
        radius = 25
        
        cv2.circle(frame, (center_x, center_y), radius, (0, 255, 0), -1)
        
        # 添加一些干扰物
        cv2.rectangle(frame, (50, 50), (100, 100), (0, 0, 255), -1)
        cv2.rectangle(frame, (width-100, height-100), (width-50, height-50), (255, 0, 0), -1)
        
        writer.write(frame)
    
    writer.release()
    print(f"✅ 测试视频创建完成: {output_path}")
    
    # 返回绿色圆圈的初始边界框
    return (100 - radius, height // 2 - radius, 2 * radius, 2 * radius)

def demo_basic_usage():
    """基础使用演示"""
    print("\n🚀 演示1: 基础使用方法")
    print("=" * 40)
    
    # 检查模型文件
    model_path = "../onnx_models/image_encoder_base_plus.onnx"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确保ONNX模型文件在正确位置")
        return
    
    # 创建测试视频
    initial_bbox = create_test_video("test_video.mp4")
    
    # 初始化跟踪器
    print("\n📦 初始化SAMURAI跟踪器...")
    tracker = SAMURAITracker(model_path, device="cpu")
    
    # 显示模型信息
    info = tracker.get_model_info()
    print(f"📋 模型信息:")
    for key, value in info.items():
        print(f"   {key}: {value}")
    
    # 执行跟踪
    print(f"\n🎯 开始跟踪...")
    results = tracker.track_video(
        video_path="test_video.mp4",
        initial_bbox=initial_bbox,
        output_path="tracking_result.mp4"
    )
    
    print(f"\n📊 跟踪结果:")
    print(f"   总帧数: {len(results)}")
    print(f"   前5帧边界框: {results[:5]}")
    print(f"   后5帧边界框: {results[-5:]}")

def demo_quick_function():
    """快速函数演示"""
    print("\n⚡ 演示2: 一行代码跟踪")
    print("=" * 30)
    
    model_path = "../onnx_models/image_encoder_base_plus.onnx"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    # 使用快速函数 - 一行代码完成跟踪
    results = quick_track(
        model_path=model_path,
        video_path="test_video.mp4", 
        bbox=(75, 215, 50, 50),  # 绿色圆圈的边界框
        output_path="quick_result.mp4"
    )
    
    print(f"✅ 快速跟踪完成，处理了 {len(results)} 帧")

def demo_single_frame():
    """单帧处理演示"""
    print("\n🖼️  演示3: 单帧处理")
    print("=" * 25)
    
    model_path = "../onnx_models/image_encoder_base_plus.onnx"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    # 创建一个测试图像
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 初始化跟踪器
    tracker = SAMURAITracker(model_path)
    
    # 处理单帧
    bbox = (100, 100, 50, 50)
    new_bbox = tracker.track_single_frame(test_image, bbox)
    
    print(f"输入边界框: {bbox}")
    print(f"输出边界框: {new_bbox}")

def main():
    """主函数"""
    print("🎯 SAMURAI ONNX 简单演示")
    print("这个演示展示了如何使用ONNX版本的SAMURAI")
    print("=" * 50)
    
    try:
        # 运行所有演示
        demo_basic_usage()
        demo_quick_function() 
        demo_single_frame()
        
        print("\n" + "=" * 50)
        print("🎉 所有演示完成!")
        print("\n📁 生成的文件:")
        print("   - test_video.mp4: 测试视频")
        print("   - tracking_result.mp4: 跟踪结果")
        print("   - quick_result.mp4: 快速跟踪结果")
        
        print("\n💡 使用提示:")
        print("   1. 确保ONNX模型文件在 ../onnx_models/ 目录")
        print("   2. 安装依赖: pip install -r ../inference_engine/requirements.txt")
        print("   3. 修改视频路径和边界框来处理你自己的视频")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("请检查:")
        print("   1. ONNX模型文件是否存在")
        print("   2. 依赖是否正确安装")
        print("   3. 文件路径是否正确")

if __name__ == "__main__":
    main()
