# SAMURAI ONNX - 视频目标跟踪系统

## 🎯 项目概述

这是SAMURAI项目的ONNX移植版本，提供高效的视频目标跟踪功能。

**核心优势:**
- ✅ **跨平台**: 支持Windows、Linux、macOS
- ✅ **轻量级**: 只需ONNX Runtime，无需复杂环境
- ✅ **易部署**: 单个模型文件即可运行
- ✅ **高性能**: 优化的推理速度

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r inference_engine/requirements.txt
```

### 2. 运行演示
```bash
cd examples
python simple_demo.py
```

### 3. 使用你的视频
```python
from inference_engine.samurai_onnx import SAMURAITracker

# 初始化跟踪器
tracker = SAMURAITracker("onnx_models/image_encoder_base_plus.onnx")

# 跟踪视频 (x, y, w, h)
results = tracker.track_video("your_video.mp4", (100, 100, 50, 50))
```

## 📁 文件结构

```
delivery_package/
├── onnx_models/                    # AI模型文件
│   └── image_encoder_base_plus.onnx (264MB)
├── inference_engine/               # 推理引擎
│   ├── samurai_onnx.py            # 主要接口
│   └── requirements.txt           # 依赖列表
├── examples/                      # 使用示例
│   └── simple_demo.py             # 简单演示
├── docs/                          # 文档
│   └── API_REFERENCE.md           # API文档
└── README.md                      # 本文件
```

## 💻 系统要求

**最低要求:**
- Python 3.8+
- 4GB RAM
- 2GB 可用磁盘空间

**推荐配置:**
- Python 3.9+
- 8GB RAM
- GPU支持 (可选)

## 🔧 API 使用

### 基础用法
```python
from inference_engine.samurai_onnx import SAMURAITracker

# 创建跟踪器
tracker = SAMURAITracker("onnx_models/image_encoder_base_plus.onnx")

# 跟踪视频
results = tracker.track_video(
    video_path="input.mp4",
    initial_bbox=(x, y, width, height),
    output_path="output.mp4"  # 可选
)

# results 是边界框列表: [(x, y, w, h), ...]
```

### 快速函数
```python
from inference_engine.samurai_onnx import quick_track

# 一行代码完成跟踪
results = quick_track("model.onnx", "video.mp4", (100, 100, 50, 50))
```

### 单帧处理
```python
import cv2

# 读取图像
frame = cv2.imread("image.jpg")

# 处理单帧
new_bbox = tracker.track_single_frame(frame, (x, y, w, h))
```

## 📊 性能指标

| 指标 | 数值 |
|------|------|
| 模型大小 | 264MB |
| 输入尺寸 | 1024×1024 |
| 推理时间 | ~1.7秒/帧 (CPU) |
| 内存使用 | ~2GB |
| 支持格式 | MP4, AVI, MOV |

## 🛠️ 高级配置

### GPU 加速
```bash
# 安装GPU版本
pip uninstall onnxruntime
pip install onnxruntime-gpu

# 使用GPU
tracker = SAMURAITracker("model.onnx", device="cuda")
```

### 批处理
```python
# 处理多个视频
videos = ["video1.mp4", "video2.mp4", "video3.mp4"]
bboxes = [(100, 100, 50, 50), (200, 150, 60, 40), (50, 75, 80, 60)]

for video, bbox in zip(videos, bboxes):
    results = tracker.track_video(video, bbox)
    print(f"{video}: {len(results)} frames processed")
```

## ❓ 常见问题

### Q: 如何选择初始边界框？
A: 在视频第一帧中手动标注目标物体的位置，格式为 (x, y, width, height)

### Q: 支持哪些视频格式？
A: 支持OpenCV能读取的所有格式：MP4, AVI, MOV, MKV等

### Q: 推理速度太慢怎么办？
A: 
1. 使用GPU: `device="cuda"`
2. 降低视频分辨率
3. 跳帧处理: 每N帧处理一次

### Q: 如何提高跟踪精度？
A: 
1. 确保初始边界框准确
2. 选择目标清晰的帧作为起始帧
3. 避免遮挡严重的场景

## 🔍 故障排除

### 模型加载失败
```
错误: 无法加载ONNX模型
解决: 检查模型文件路径和完整性
```

### 内存不足
```
错误: OOM (Out of Memory)
解决: 
1. 关闭其他程序释放内存
2. 降低视频分辨率
3. 使用更小的模型
```

### 推理速度慢
```
问题: 处理速度很慢
解决:
1. 使用GPU加速
2. 检查CPU使用率
3. 优化视频编码格式
```

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 操作系统和Python版本
- 错误信息的完整输出
- 输入视频的基本信息
- 使用的边界框坐标

## 📄 许可证

本项目遵循原SAMURAI项目的许可证条款。

## 🔄 更新日志

**v1.0.0** (2025-08-25)
- ✅ 初始ONNX移植版本
- ✅ 基础视频跟踪功能
- ✅ CPU推理支持
- ✅ 简化的API接口

---

**快速验证安装:**
```bash
cd examples && python simple_demo.py
```

如果看到 "🎉 所有演示完成!" 说明安装成功！
