#!/usr/bin/env python3
"""
SAMURAI ONNX 快速开始脚本
一键验证和演示
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        return False

def main():
    print("🚀 SAMURAI ONNX 快速开始")
    print("=" * 30)
    
    # 1. 验证交付包
    if not run_command("python validation/verify_delivery.py", "验证交付包"):
        print("请先解决验证问题")
        return
    
    # 2. 安装依赖
    if not run_command("pip install -r inference_engine/requirements.txt", "安装依赖"):
        print("请手动安装依赖")
        return
    
    # 3. 运行演示
    os.chdir("examples")
    if not run_command("python simple_demo.py", "运行演示"):
        print("演示运行失败")
        return
    
    print("\n🎉 快速开始完成!")
    print("查看生成的视频文件了解跟踪效果")

if __name__ == "__main__":
    main()
