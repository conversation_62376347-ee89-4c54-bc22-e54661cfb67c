LICENSE
LICENSE_cctorch
MANIFEST.in
README.md
pyproject.toml
setup.py
SAM_2.egg-info/PKG-INFO
SAM_2.egg-info/SOURCES.txt
SAM_2.egg-info/dependency_links.txt
SAM_2.egg-info/requires.txt
SAM_2.egg-info/top_level.txt
sam2/__init__.py
sam2/automatic_mask_generator.py
sam2/build_sam.py
sam2/sam2_hiera_b+.yaml
sam2/sam2_hiera_l.yaml
sam2/sam2_hiera_s.yaml
sam2/sam2_hiera_t.yaml
sam2/sam2_image_predictor.py
sam2/sam2_video_predictor.py
sam2/configs/sam2/sam2_hiera_b+.yaml
sam2/configs/sam2/sam2_hiera_l.yaml
sam2/configs/sam2/sam2_hiera_s.yaml
sam2/configs/sam2/sam2_hiera_t.yaml
sam2/configs/sam2.1/sam2.1_hiera_b+.yaml
sam2/configs/sam2.1/sam2.1_hiera_l.yaml
sam2/configs/sam2.1/sam2.1_hiera_s.yaml
sam2/configs/sam2.1/sam2.1_hiera_t.yaml
sam2/configs/sam2.1_training/sam2.1_hiera_b+_MOSE_finetune.yaml
sam2/configs/samurai/sam2.1_hiera_b+.yaml
sam2/configs/samurai/sam2.1_hiera_l.yaml
sam2/configs/samurai/sam2.1_hiera_s.yaml
sam2/configs/samurai/sam2.1_hiera_t.yaml
sam2/modeling/__init__.py
sam2/modeling/memory_attention.py
sam2/modeling/memory_encoder.py
sam2/modeling/position_encoding.py
sam2/modeling/sam2_base.py
sam2/modeling/sam2_utils.py
sam2/modeling/backbones/__init__.py
sam2/modeling/backbones/hieradet.py
sam2/modeling/backbones/image_encoder.py
sam2/modeling/backbones/utils.py
sam2/modeling/sam/__init__.py
sam2/modeling/sam/mask_decoder.py
sam2/modeling/sam/prompt_encoder.py
sam2/modeling/sam/transformer.py
sam2/utils/__init__.py
sam2/utils/amg.py
sam2/utils/kalman_filter.py
sam2/utils/misc.py
sam2/utils/transforms.py
training/__init__.py
training/loss_fns.py
training/optimizer.py
training/train.py
training/trainer.py
training/dataset/__init__.py
training/dataset/sam2_datasets.py
training/dataset/transforms.py
training/dataset/utils.py
training/dataset/vos_dataset.py
training/dataset/vos_raw_dataset.py
training/dataset/vos_sampler.py
training/dataset/vos_segment_loader.py
training/model/__init__.py
training/model/sam2.py
training/utils/__init__.py
training/utils/checkpoint_utils.py
training/utils/data_utils.py
training/utils/distributed.py
training/utils/logger.py
training/utils/train_utils.py