"""
最终的端到端SAMURAI ONNX系统测试
验证修复后的推理引擎和端到端模型
"""

import sys
import os
import numpy as np
import time

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

def test_fixed_inference_engine():
    """测试修复后的推理引擎"""
    
    print("🚀 测试修复后的SAMURAI ONNX推理引擎")
    print("=" * 50)
    
    try:
        from onnx_inference import SAMURAIONNXPredictor
        
        # 初始化预测器
        print("1. 初始化预测器...")
        predictor = SAMURAIONNXPredictor("onnx_models", device="cpu", use_end_to_end=True)
        print("   ✅ 预测器初始化成功")
        
        # 创建测试图像
        print("\n2. 准备测试数据...")
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_bbox = (100, 100, 200, 200)  # x1, y1, x2, y2
        
        print(f"   测试图像: {test_image.shape}")
        print(f"   测试边界框: {test_bbox}")
        
        # 测试单帧预测
        print("\n3. 测试单帧预测...")
        start_time = time.time()
        mask, confidence, memory_features = predictor.predict_mask(test_image, test_bbox)
        end_time = time.time()
        
        print(f"   ✅ 单帧预测成功!")
        print(f"   推理时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"   掩码形状: {mask.shape}")
        print(f"   掩码范围: [{mask.min()}, {mask.max()}]")
        print(f"   置信度: {confidence:.3f}")
        print(f"   内存特征: {memory_features.shape}")
        
        # 测试多帧预测（模拟视频）
        print("\n4. 测试多帧预测...")
        total_time = 0
        num_frames = 5
        
        for i in range(num_frames):
            # 创建略有变化的图像和边界框
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            bbox = (100 + i*5, 100 + i*3, 200 + i*5, 200 + i*3)
            
            start_time = time.time()
            mask, confidence, memory_features = predictor.predict_mask(frame, bbox)
            end_time = time.time()
            
            frame_time = (end_time - start_time) * 1000
            total_time += frame_time
            
            print(f"   帧 {i+1}: {frame_time:.2f}ms, 置信度: {confidence:.3f}")
        
        avg_time = total_time / num_frames
        fps = 1000 / avg_time
        
        print(f"\n   📊 多帧性能统计:")
        print(f"   平均推理时间: {avg_time:.2f}ms")
        print(f"   理论FPS: {fps:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_tracking_simulation():
    """测试视频跟踪模拟"""
    
    print("\n🎬 测试视频跟踪模拟")
    print("=" * 30)
    
    try:
        from onnx_inference import SAMURAIONNXPredictor
        
        # 初始化预测器
        predictor = SAMURAIONNXPredictor("onnx_models", device="cpu")
        
        # 模拟视频序列
        print("创建模拟视频序列...")
        frames = []
        bboxes = []
        
        # 创建10帧的简单运动序列
        for i in range(10):
            # 创建帧
            frame = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
            
            # 模拟目标运动（从左到右）
            x = 50 + i * 20
            y = 100 + int(10 * np.sin(i * 0.5))  # 轻微上下运动
            bbox = (x, y, x + 50, y + 50)
            
            frames.append(frame)
            bboxes.append(bbox)
        
        print(f"   创建了 {len(frames)} 帧序列")
        
        # 跟踪测试
        print("\n开始跟踪测试...")
        results = []
        total_time = 0
        
        for i, (frame, true_bbox) in enumerate(zip(frames, bboxes)):
            if i == 0:
                # 第一帧使用真实边界框
                current_bbox = true_bbox
            else:
                # 后续帧使用上一帧的预测结果作为搜索区域
                current_bbox = results[-1]['predicted_bbox']
            
            start_time = time.time()
            mask, confidence, memory_features = predictor.predict_mask(frame, current_bbox)
            end_time = time.time()
            
            frame_time = (end_time - start_time) * 1000
            total_time += frame_time
            
            # 从掩码计算新的边界框（简化版本）
            if mask.any():
                y_indices, x_indices = np.where(mask)
                if len(x_indices) > 0:
                    x1, x2 = x_indices.min(), x_indices.max()
                    y1, y2 = y_indices.min(), y_indices.max()
                    predicted_bbox = (x1, y1, x2, y2)
                else:
                    predicted_bbox = current_bbox
            else:
                predicted_bbox = current_bbox
            
            # 计算跟踪精度（IoU）
            def calculate_iou(box1, box2):
                x1_1, y1_1, x2_1, y2_1 = box1
                x1_2, y1_2, x2_2, y2_2 = box2
                
                # 计算交集
                x1_i = max(x1_1, x1_2)
                y1_i = max(y1_1, y1_2)
                x2_i = min(x2_1, x2_2)
                y2_i = min(y2_1, y2_2)
                
                if x2_i <= x1_i or y2_i <= y1_i:
                    return 0.0
                
                intersection = (x2_i - x1_i) * (y2_i - y1_i)
                area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
                area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
                union = area1 + area2 - intersection
                
                return intersection / union if union > 0 else 0.0
            
            iou = calculate_iou(predicted_bbox, true_bbox)
            
            result = {
                'frame': i,
                'true_bbox': true_bbox,
                'predicted_bbox': predicted_bbox,
                'confidence': confidence,
                'iou': iou,
                'time_ms': frame_time
            }
            results.append(result)
            
            print(f"   帧 {i+1:2d}: IoU={iou:.3f}, 置信度={confidence:.3f}, 时间={frame_time:.1f}ms")
        
        # 统计结果
        avg_time = total_time / len(frames)
        avg_iou = np.mean([r['iou'] for r in results])
        avg_confidence = np.mean([r['confidence'] for r in results])
        
        print(f"\n   📊 跟踪性能统计:")
        print(f"   平均推理时间: {avg_time:.2f}ms")
        print(f"   平均IoU: {avg_iou:.3f}")
        print(f"   平均置信度: {avg_confidence:.3f}")
        print(f"   理论FPS: {1000/avg_time:.2f}")
        
        # 判断跟踪质量
        if avg_iou > 0.7:
            print("   🎉 跟踪质量: 优秀")
        elif avg_iou > 0.5:
            print("   ✅ 跟踪质量: 良好")
        elif avg_iou > 0.3:
            print("   ⚠️  跟踪质量: 一般")
        else:
            print("   ❌ 跟踪质量: 较差")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频跟踪测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_comparison():
    """测试不同模型的性能对比"""
    
    print("\n📊 模型性能对比测试")
    print("=" * 30)
    
    try:
        from onnx_inference import SAMURAIONNXPredictor
        
        # 测试不同配置的预测器
        configs = [
            ("端到端模式", {"use_end_to_end": True}),
            ("组件模式", {"use_end_to_end": False})
        ]
        
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_bbox = (100, 100, 200, 200)
        
        results = {}
        
        for config_name, config in configs:
            print(f"\n测试 {config_name}:")
            
            try:
                predictor = SAMURAIONNXPredictor("onnx_models", device="cpu", **config)
                
                # 预热
                predictor.predict_mask(test_image, test_bbox)
                
                # 测试多次取平均
                times = []
                confidences = []
                
                for _ in range(3):
                    start_time = time.time()
                    mask, confidence, memory_features = predictor.predict_mask(test_image, test_bbox)
                    end_time = time.time()
                    
                    times.append((end_time - start_time) * 1000)
                    confidences.append(confidence)
                
                avg_time = np.mean(times)
                avg_confidence = np.mean(confidences)
                
                results[config_name] = {
                    'time': avg_time,
                    'confidence': avg_confidence,
                    'fps': 1000 / avg_time
                }
                
                print(f"   ✅ 平均时间: {avg_time:.2f}ms")
                print(f"   ✅ 平均置信度: {avg_confidence:.3f}")
                print(f"   ✅ 理论FPS: {1000/avg_time:.2f}")
                
            except Exception as e:
                print(f"   ❌ {config_name} 测试失败: {e}")
        
        # 显示对比结果
        if len(results) > 1:
            print(f"\n📈 性能对比总结:")
            sorted_results = sorted(results.items(), key=lambda x: x[1]['time'])
            
            for i, (name, result) in enumerate(sorted_results):
                rank = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
                print(f"   {rank} {name}: {result['time']:.2f}ms ({result['fps']:.2f} FPS)")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🎯 SAMURAI 端到端系统最终测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "onnx_models/image_encoder_base_plus.onnx",
        "onnx_models/samurai_mock_end_to_end.onnx",
        "scripts/onnx_inference.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print("❌ 缺少必要文件:")
        for f in missing_files:
            print(f"   - {f}")
        return
    
    print("✅ 所有必要文件存在")
    
    # 运行测试
    tests = [
        ("推理引擎测试", test_fixed_inference_engine),
        ("视频跟踪模拟", test_video_tracking_simulation),
        ("模型性能对比", test_model_comparison)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 出现异常: {e}")
            results[test_name] = False
    
    # 最终总结
    print("\n" + "="*60)
    print("🏆 最终测试总结")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    success_rate = (passed / total) * 100
    print(f"\n📊 测试通过率: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 所有测试通过！SAMURAI端到端ONNX系统完全工作正常！")
        print("🚀 系统已准备好进行生产部署！")
    elif success_rate >= 80:
        print("✅ 大部分测试通过，系统基本可用")
        print("🔧 建议修复失败的测试以获得最佳性能")
    elif success_rate >= 50:
        print("⚠️  部分测试通过，系统需要进一步调试")
    else:
        print("❌ 大部分测试失败，系统需要重大修复")
    
    print(f"\n💡 系统状态:")
    print(f"   - 端到端模型: 可用")
    print(f"   - 推理引擎: {'正常' if results.get('推理引擎测试', False) else '需要修复'}")
    print(f"   - 视频跟踪: {'正常' if results.get('视频跟踪模拟', False) else '需要修复'}")

if __name__ == "__main__":
    main()
