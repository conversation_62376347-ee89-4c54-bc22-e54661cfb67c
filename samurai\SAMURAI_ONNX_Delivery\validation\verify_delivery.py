"""
交付包验证脚本
客户收到交付包后运行此脚本验证完整性
"""

import os
import sys
import importlib.util
from pathlib import Path

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"✅ {description}: {file_path} ({size_mb:.1f}MB)")
        return True
    else:
        print(f"❌ {description}: {file_path} - 文件缺失")
        return False

def check_dependencies():
    """检查Python依赖"""
    print("\n📦 检查Python依赖...")
    
    required_packages = [
        ("onnxruntime", "ONNX Runtime"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy")
    ]
    
    missing = []
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name}: 已安装")
        except ImportError:
            print(f"❌ {name}: 未安装")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  缺少依赖: {', '.join(missing)}")
        print("请运行: pip install -r inference_engine/requirements.txt")
        return False
    
    return True

def test_model_loading():
    """测试模型加载"""
    print("\n🧪 测试ONNX模型加载...")
    
    model_path = "onnx_models/image_encoder_base_plus.onnx"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    try:
        import onnxruntime as ort
        session = ort.InferenceSession(model_path)
        print("✅ ONNX模型加载成功")
        
        # 显示模型信息
        inputs = session.get_inputs()
        outputs = session.get_outputs()
        print(f"   输入: {inputs[0].name} {inputs[0].shape}")
        print(f"   输出数量: {len(outputs)}")
        
        return True
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_inference_engine():
    """测试推理引擎"""
    print("\n⚡ 测试推理引擎...")
    
    # 添加推理引擎到路径
    sys.path.insert(0, 'inference_engine')
    
    try:
        from samurai_onnx import SAMURAITracker
        
        # 创建跟踪器
        tracker = SAMURAITracker("onnx_models/image_encoder_base_plus.onnx")
        print("✅ 推理引擎初始化成功")
        
        # 获取模型信息
        info = tracker.get_model_info()
        print(f"   设备: {info['device']}")
        print(f"   输入形状: {info['input_shape']}")
        
        return True
    except Exception as e:
        print(f"❌ 推理引擎测试失败: {e}")
        return False

def test_example_code():
    """测试示例代码"""
    print("\n📝 检查示例代码...")
    
    example_path = "examples/simple_demo.py"
    if not os.path.exists(example_path):
        print(f"❌ 示例文件不存在: {example_path}")
        return False
    
    # 检查代码语法
    try:
        with open(example_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, example_path, 'exec')
        print("✅ 示例代码语法正确")
        return True
    except SyntaxError as e:
        print(f"❌ 示例代码语法错误: {e}")
        return False

def generate_delivery_report():
    """生成交付报告"""
    print("\n📋 生成交付验证报告...")
    
    report = []
    report.append("# SAMURAI ONNX 交付验证报告")
    report.append(f"验证时间: {__import__('datetime').datetime.now()}")
    report.append("")
    
    # 文件清单
    report.append("## 文件清单")
    files_to_check = [
        ("onnx_models/image_encoder_base_plus.onnx", "ONNX模型文件"),
        ("inference_engine/samurai_onnx.py", "推理引擎"),
        ("inference_engine/requirements.txt", "依赖列表"),
        ("examples/simple_demo.py", "使用示例"),
        ("README.md", "使用说明"),
    ]
    
    all_files_ok = True
    for file_path, desc in files_to_check:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        report.append(f"- {status} {desc}: `{file_path}`")
        if not exists:
            all_files_ok = False
    
    report.append("")
    
    # 功能测试
    report.append("## 功能测试")
    tests = [
        ("依赖检查", check_dependencies()),
        ("模型加载", test_model_loading()),
        ("推理引擎", test_inference_engine()),
        ("示例代码", test_example_code()),
    ]
    
    all_tests_ok = True
    for test_name, result in tests:
        status = "✅" if result else "❌"
        report.append(f"- {status} {test_name}")
        if not result:
            all_tests_ok = False
    
    report.append("")
    
    # 总结
    if all_files_ok and all_tests_ok:
        report.append("## ✅ 验证结果: 通过")
        report.append("交付包完整，可以正常使用。")
    else:
        report.append("## ❌ 验证结果: 失败")
        report.append("交付包存在问题，请联系技术支持。")
    
    # 保存报告
    with open("delivery_verification_report.md", "w", encoding="utf-8") as f:
        f.write("\n".join(report))
    
    print("✅ 验证报告已保存: delivery_verification_report.md")

def main():
    """主验证流程"""
    print("🔍 SAMURAI ONNX 交付包验证")
    print("=" * 50)
    
    print("📁 检查核心文件...")
    
    # 检查核心文件
    core_files = [
        ("onnx_models/image_encoder_base_plus.onnx", "ONNX模型"),
        ("inference_engine/samurai_onnx.py", "推理引擎"),
        ("inference_engine/requirements.txt", "依赖文件"),
        ("examples/simple_demo.py", "演示脚本"),
        ("README.md", "说明文档"),
    ]
    
    files_ok = True
    for file_path, desc in core_files:
        if not check_file_exists(file_path, desc):
            files_ok = False
    
    if not files_ok:
        print("\n❌ 核心文件检查失败，交付包不完整")
        return
    
    # 运行功能测试
    deps_ok = check_dependencies()
    model_ok = test_model_loading()
    engine_ok = test_inference_engine()
    example_ok = test_example_code()
    
    # 生成报告
    generate_delivery_report()
    
    # 最终结果
    print("\n" + "=" * 50)
    if all([files_ok, deps_ok, model_ok, engine_ok, example_ok]):
        print("🎉 验证完成: 交付包完整且功能正常!")
        print("\n📖 下一步:")
        print("   1. 阅读 README.md 了解使用方法")
        print("   2. 运行 examples/simple_demo.py 查看演示")
        print("   3. 根据需要修改代码处理你的视频")
    else:
        print("❌ 验证失败: 交付包存在问题")
        print("\n🔧 建议:")
        print("   1. 检查文件完整性")
        print("   2. 安装缺失的依赖")
        print("   3. 联系技术支持")

if __name__ == "__main__":
    main()
