Metadata-Version: 2.1
Name: omegaconf
Version: 2.3.0
Summary: A flexible configuration library
Home-page: https://github.com/omry/omegaconf
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
Keywords: yaml configuration config
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: antlr4-python3-runtime (==4.9.*)
Requires-Dist: PyYAML (>=5.1.0)
Requires-Dist: dataclasses ; python_version == "3.6"

# OmegaConf
|  | Description |
| --- | --- |
| Project | [![PyPI version](https://badge.fury.io/py/omegaconf.svg)](https://badge.fury.io/py/omegaconf)[![Downloads](https://pepy.tech/badge/omegaconf/month)](https://pepy.tech/project/omegaconf?versions=1.4.*&versions=2.0.*&versions=2.1.*)![PyPI - Python Version](https://img.shields.io/pypi/pyversions/omegaconf.svg) |
| Code quality| [![CircleCI](https://img.shields.io/circleci/build/github/omry/omegaconf?logo=s&token=5de2f8dc2a0dd78438520575431aa533150806e3)](https://circleci.com/gh/omry/omegaconf)[![Coverage Status](https://coveralls.io/repos/github/omry/omegaconf/badge.svg)](https://coveralls.io/github/omry/omegaconf)[![Total alerts](https://img.shields.io/lgtm/alerts/g/omry/omegaconf.svg?logo=lgtm&logoWidth=18)](https://lgtm.com/projects/g/omry/omegaconf/alerts/)[![Language grade: Python](https://img.shields.io/lgtm/grade/python/g/omry/omegaconf.svg?logo=lgtm&logoWidth=18)](https://lgtm.com/projects/g/omry/omegaconf/context:python)|
| Docs and support |[![Documentation Status](https://readthedocs.org/projects/omegaconf/badge/?version=2.0_branch)](https://omegaconf.readthedocs.io/en/2.1_branch/)[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/omry/omegaconf/master?filepath=docs%2Fnotebook%2FTutorial.ipynb)|


OmegaConf is a hierarchical configuration system, with support for merging configurations from multiple sources (YAML config files, dataclasses/objects and CLI arguments)
providing a consistent API regardless of how the configuration was created.

## Releases

### Stable (2.2)
OmegaConf 2.2 is the current stable version.
* [What's new](https://github.com/omry/omegaconf/releases/tag/v2.2.1)
* [Documentation](https://omegaconf.readthedocs.io/en/2.2_branch/)
* [Source code](https://github.com/omry/omegaconf/tree/2.2_branch)

Install with `pip install --upgrade omegaconf`

### Previous release (2.1)
OmegaConf 2.1 is the current stable version.
* [What's new](https://github.com/omry/omegaconf/releases/tag/v2.1.1)
* [Documentation](https://omegaconf.readthedocs.io/en/2.1_branch/)
* [Slides](https://docs.google.com/presentation/d/e/2PACX-1vT_UIV7hCnquIbLUm4NnkUpXvPEh33IKiUEvPRF850WKA8opOlZOszjKdZ3tPmf8u7hGNP6HpqS-NT5/pub?start=false&loop=false&delayms=3000)
* [Source code](https://github.com/omry/omegaconf/tree/2.1_branch)

Install with `pip install omegaconf==2.1`

### Previous release (2.0)

* [What's new](https://github.com/omry/omegaconf/releases/tag/v2.0.0)
* [Documentation](https://omegaconf.readthedocs.io/en/2.0_branch/)
* [Slides](https://docs.google.com/presentation/d/e/2PACX-1vT_UIV7hCnquIbLUm4NnkUpXvPEh33IKiUEvPRF850WKA8opOlZOszjKdZ3tPmf8u7hGNP6HpqS-NT5/pub?start=false&loop=false&delayms=3000)
* [Source code](https://github.com/omry/omegaconf/tree/2.0_branch)

Install with `pip install omegaconf==2.0.6`

## Live tutorial
Run the live tutorial: [![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/omry/omegaconf/master?filepath=docs%2Fnotebook%2FTutorial.ipynb)
